// Business Analytics Controller Example
// Implementation example for the business analytics metrics

import { Controller, Get, Query, Req, Version } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { BusinessAnalyticsService } from './business-analytics-prisma-queries';

@ApiTags('Business Analytics')
@Controller('business-analytics')
export class BusinessAnalyticsController {
  constructor(private readonly businessAnalyticsService: BusinessAnalyticsService) {}

  @ApiOperation({ summary: 'Get total deal value by portfolio' })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiResponse({ status: 200, description: 'Total deal value grouped by portfolio' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiBearerAuth()
  @Get('deal-value-by-portfolio')
  @Version('1')
  async getTotalDealValueByPortfolio(
    @Req() request: Request,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.businessAnalyticsService.getTotalDealValueByPortfolio(
      customerId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: {
        portfolios: metrics,
        totalValue: metrics.reduce((sum, p) => sum + p.totalDealValue, 0),
        totalDeals: metrics.reduce((sum, p) => sum + p.dealCount, 0),
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
      },
    };
  }

  @ApiOperation({ summary: 'Get average ticket value' })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date-time',
    required: false,
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date-time',
    required: false,
  })
  @ApiResponse({ status: 200, description: 'Average ticket value' })
  @ApiBearerAuth()
  @Get('average-ticket-value')
  @Version('1')
  async getAverageTicketValue(
    @Req() request: Request,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ) {
    const { customerId } = request['user'];

    const averageTicket = await this.businessAnalyticsService.getAverageTicketValue(
      customerId,
      startDate,
      endDate,
    );

    const formattedValue = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(averageTicket / 100);

    return {
      statusCode: 200,
      data: {
        averageTicketValue: averageTicket,
        formattedValue,
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
      },
    };
  }

  @ApiOperation({ summary: 'Get total portfolio quantity' })
  @ApiResponse({ status: 200, description: 'Total portfolio quantity' })
  @ApiBearerAuth()
  @Get('total-portfolio-quantity')
  @Version('1')
  async getTotalPortfolioQuantity(@Req() request: Request) {
    const { customerId } = request['user'];

    const totalQuantity = await this.businessAnalyticsService.getTotalPortfolioQuantity(customerId);

    return {
      statusCode: 200,
      data: {
        totalPortfolioQuantity: Number(totalQuantity),
      },
    };
  }

  @ApiOperation({ summary: 'Get successful negotiations with interactions by date' })
  @ApiQuery({ name: 'startDate', type: String, format: 'date-time', required: true })
  @ApiQuery({ name: 'endDate', type: String, format: 'date-time', required: true })
  @ApiResponse({ status: 200, description: 'Daily successful negotiations with interactions' })
  @ApiBearerAuth()
  @Get('successful-negotiations-with-interactions')
  @Version('1')
  async getSuccessfulNegotiationsWithInteractions(
    @Req() request: Request,
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.businessAnalyticsService.getSuccessfulNegotiationsWithInteractions(
      customerId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: {
        dailyMetrics: metrics,
        totalSuccessful: metrics.reduce((sum, m) => sum + m.value, 0),
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
    };
  }

  @ApiOperation({ summary: 'Get daily imports (portfolio creation)' })
  @ApiQuery({ name: 'startDate', type: String, format: 'date-time', required: true })
  @ApiQuery({ name: 'endDate', type: String, format: 'date-time', required: true })
  @ApiResponse({ status: 200, description: 'Daily portfolio imports' })
  @ApiBearerAuth()
  @Get('daily-imports')
  @Version('1')
  async getDailyImports(
    @Req() request: Request,
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.businessAnalyticsService.getDailyImports(
      customerId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: {
        dailyImports: metrics,
        totalImports: metrics.reduce((sum, m) => sum + m.value, 0),
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
    };
  }

  @ApiOperation({ summary: 'Get daily initiated contacts (first messages sent)' })
  @ApiQuery({ name: 'startDate', type: String, format: 'date-time', required: true })
  @ApiQuery({ name: 'endDate', type: String, format: 'date-time', required: true })
  @ApiResponse({ status: 200, description: 'Daily initiated contacts' })
  @ApiBearerAuth()
  @Get('daily-initiated-contacts')
  @Version('1')
  async getDailyInitiatedContacts(
    @Req() request: Request,
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.businessAnalyticsService.getDailyInitiatedContacts(
      customerId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: {
        dailyContacts: metrics,
        totalContacts: metrics.reduce((sum, m) => sum + m.value, 0),
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
    };
  }

  @ApiOperation({ summary: 'Get success rate metrics' })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date-time',
    required: false,
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date-time',
    required: false,
  })
  @ApiResponse({ status: 200, description: 'Success rate metrics' })
  @ApiBearerAuth()
  @Get('success-rate')
  @Version('1')
  async getSuccessRate(
    @Req() request: Request,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.businessAnalyticsService.getSuccessRate(
      customerId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: {
        ...metrics,
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
      },
    };
  }

  @ApiOperation({ summary: 'Get daily recovered value' })
  @ApiQuery({ name: 'startDate', type: String, format: 'date-time', required: true })
  @ApiQuery({ name: 'endDate', type: String, format: 'date-time', required: true })
  @ApiResponse({ status: 200, description: 'Daily recovered value' })
  @ApiBearerAuth()
  @Get('daily-recovered-value')
  @Version('1')
  async getDailyRecoveredValue(
    @Req() request: Request,
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
  ) {
    const { customerId } = request['user'];

    const metrics = await this.businessAnalyticsService.getDailyRecoveredValue(
      customerId,
      startDate,
      endDate,
    );

    const totalRecovered = metrics.reduce((sum, m) => sum + m.value, 0);
    const formattedTotal = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(totalRecovered / 100);

    return {
      statusCode: 200,
      data: {
        dailyRecoveredValue: metrics.map(m => ({
          ...m,
          formattedValue: new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
          }).format(m.value / 100),
        })),
        totalRecovered,
        formattedTotal,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
    };
  }

  @ApiOperation({ summary: 'Get comprehensive dashboard summary' })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date-time',
    required: false,
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date-time',
    required: false,
  })
  @ApiResponse({ status: 200, description: 'Comprehensive dashboard metrics' })
  @ApiBearerAuth()
  @Get('dashboard-summary')
  @Version('1')
  async getDashboardSummary(
    @Req() request: Request,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ) {
    const { customerId } = request['user'];

    const summary = await this.businessAnalyticsService.getDashboardSummary(
      customerId,
      startDate,
      endDate,
    );

    // Format currency values
    const formatCurrency = (value: number) =>
      new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
      }).format(value / 100);

    return {
      statusCode: 200,
      data: {
        ...summary,
        formattedTotalRecoveredValue: formatCurrency(summary.totalRecoveredValue),
        formattedAverageTicketValue: formatCurrency(summary.averageTicketValue),
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
      },
    };
  }

  @ApiOperation({ summary: 'Get all business analytics metrics' })
  @ApiQuery({ name: 'startDate', type: String, format: 'date-time', required: true })
  @ApiQuery({ name: 'endDate', type: String, format: 'date-time', required: true })
  @ApiResponse({ status: 200, description: 'All business analytics metrics' })
  @ApiBearerAuth()
  @Get('all-metrics')
  @Version('1')
  async getAllMetrics(
    @Req() request: Request,
    @Query('startDate') startDate: Date,
    @Query('endDate') endDate: Date,
  ) {
    const { customerId } = request['user'];

    const [
      dealValueByPortfolio,
      averageTicketValue,
      totalPortfolioQuantity,
      successfulNegotiationsWithInteractions,
      dailyImports,
      dailyInitiatedContacts,
      successRate,
      dailyRecoveredValue,
      dashboardSummary,
    ] = await Promise.all([
      this.businessAnalyticsService.getTotalDealValueByPortfolio(customerId, startDate, endDate),
      this.businessAnalyticsService.getAverageTicketValue(customerId, startDate, endDate),
      this.businessAnalyticsService.getTotalPortfolioQuantity(customerId),
      this.businessAnalyticsService.getSuccessfulNegotiationsWithInteractions(customerId, startDate, endDate),
      this.businessAnalyticsService.getDailyImports(customerId, startDate, endDate),
      this.businessAnalyticsService.getDailyInitiatedContacts(customerId, startDate, endDate),
      this.businessAnalyticsService.getSuccessRate(customerId, startDate, endDate),
      this.businessAnalyticsService.getDailyRecoveredValue(customerId, startDate, endDate),
      this.businessAnalyticsService.getDashboardSummary(customerId, startDate, endDate),
    ]);

    return {
      statusCode: 200,
      data: {
        dealValueByPortfolio,
        averageTicketValue,
        totalPortfolioQuantity: Number(totalPortfolioQuantity),
        successfulNegotiationsWithInteractions,
        dailyImports,
        dailyInitiatedContacts,
        successRate,
        dailyRecoveredValue,
        dashboardSummary,
        period: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        },
      },
    };
  }
}
