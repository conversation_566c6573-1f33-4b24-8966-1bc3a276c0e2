{"dashboard": {"id": null, "title": "Business Analytics - Customer Metrics", "tags": ["business", "analytics", "metrics"], "style": "dark", "timezone": "browser", "refresh": "5m", "schemaVersion": 39, "version": 1, "time": {"from": "now-30d", "to": "now"}, "templating": {"list": [{"name": "customer_id", "type": "custom", "label": "Customer", "query": "613d0cdd-e8fb-45d0-b9b7-6ce351fb8083 : <PERSON><PERSON> Tech,8b5c9f2a-1e4d-4a8b-9c3e-7f6a5b2d8e9c : <PERSON><PERSON><PERSON><PERSON>,2a7b8c3d-4e5f-6a7b-8c9d-0e1f2a3b4c5d : Prod Hiring,f3e4d5c6-b7a8-9c0d-1e2f-3a4b5c6d7e8f : Stone,9c8b7a6f-5e4d-3c2b-1a09-8f7e6d5c4b3a : <PERSON> Business,4f5e6d7c-8b9a-0c1d-2e3f-4a5b6c7d8e9f : Versa<PERSON>redi,7e8f9a0b-1c2d-3e4f-5a6b-7c8d9e0f1a2b : Intro Hiring Talk Dem,1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d : <PERSON><PERSON><PERSON>,5c6d7e8f-9a0b-1c2d-3e4f-5a6b7c8d9e0f : <PERSON><PERSON>,9e0f1a2b-3c4d-5e6f-7a8b-9c0d1e2f3a4b : NEF,3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d : Grupo Skill,7c8d9e0f-1a2b-3c4d-5e6f-7a8b9c0d1e2f : Innova Energia,1e2f3a4b-5c6d-7e8f-9a0b-1c2d3e4f5a6b : Matrix Energia,5a6b7c8d-9e0f-1a2b-3c4d-5e6f7a8b9c0d : Pherfil Energia,9c0d1e2f-3a4b-5c6d-7e8f-9a0b1c2d3e4f : Stone Energia,3e4f5a6b-7c8d-9e0f-1a2b-3c4d5e6f7a8b : Dev Business Energia,7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d : VersaCredi Energia,1c2d3e4f-5a6b-7c8d-9e0f-1a2b3c4d5e6f : Intro Hiring Energia,5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b : Grupo Pedrotti Energia,9a0b1c2d-3e4f-5a6b-7c8d-9e0f1a2b3c4d : Cia de Talentos Energia,3c4d5e6f-7a8b-9c0d-1e2f-3a4b5c6d7e8f : NEF Energia", "refresh": 0, "includeAll": false, "multi": false, "current": {"selected": false, "text": "Select customer", "value": ""}, "options": [{"text": "Innova Tech", "value": "613d0cdd-e8fb-45d0-b9b7-6ce351fb8083", "selected": false}, {"text": "Pherfil", "value": "8b5c9f2a-1e4d-4a8b-9c3e-7f6a5b2d8e9c", "selected": false}, {"text": "Prod Hiring", "value": "2a7b8c3d-4e5f-6a7b-8c9d-0e1f2a3b4c5d", "selected": false}, {"text": "Stone", "value": "f3e4d5c6-b7a8-9c0d-1e2f-3a4b5c6d7e8f", "selected": false}, {"text": "Dev Business", "value": "9c8b7a6f-5e4d-3c2b-1a09-8f7e6d5c4b3a", "selected": false}, {"text": "VersaCredi", "value": "4f5e6d7c-8b9a-0c1d-2e3f-4a5b6c7d8e9f", "selected": false}, {"text": "Intro Hiring Talk Dem", "value": "7e8f9a0b-1c2d-3e4f-5a6b-7c8d9e0f1a2b", "selected": false}, {"text": "Grupo <PERSON>", "value": "1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d", "selected": false}, {"text": "Cia de Talentos", "value": "5c6d7e8f-9a0b-1c2d-3e4f-5a6b7c8d9e0f", "selected": false}, {"text": "NEF", "value": "9e0f1a2b-3c4d-5e6f-7a8b-9c0d1e2f3a4b", "selected": false}, {"text": "Grupo Skill", "value": "3a4b5c6d-7e8f-9a0b-1c2d-3e4f5a6b7c8d", "selected": false}, {"text": "Innova Energia", "value": "7c8d9e0f-1a2b-3c4d-5e6f-7a8b9c0d1e2f", "selected": false}, {"text": "Matrix Energia", "value": "1e2f3a4b-5c6d-7e8f-9a0b-1c2d3e4f5a6b", "selected": true}, {"text": "Pherfil Energia", "value": "5a6b7c8d-9e0f-1a2b-3c4d-5e6f7a8b9c0d", "selected": false}, {"text": "Stone Energia", "value": "9c0d1e2f-3a4b-5c6d-7e8f-9a0b1c2d3e4f", "selected": false}, {"text": "Dev Business Energia", "value": "3e4f5a6b-7c8d-9e0f-1a2b-3c4d5e6f7a8b", "selected": false}, {"text": "VersaCredi Energia", "value": "7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d", "selected": false}, {"text": "Intro Hiring Energia", "value": "1c2d3e4f-5a6b-7c8d-9e0f-1a2b3c4d5e6f", "selected": false}, {"text": "Grupo Pedrotti Energia", "value": "5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b", "selected": false}, {"text": "Cia de Talentos Energia", "value": "9a0b1c2d-3e4f-5a6b-7c8d-9e0f1a2b3c4d", "selected": false}, {"text": "NEF Energia", "value": "3c4d5e6f-7a8b-9c0d-1e2f-3a4b5c6d7e8f", "selected": false}]}]}, "panels": [{"id": 1, "title": "📊 Customer Business Metrics Summary", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "datasource": {"type": "postgres", "uid": "transcendence-postgresql-datasource"}, "targets": [{"rawSql": "WITH portfolio_stats AS (\n    SELECT \n        COUNT(DISTINCT p.id) as total_portfolios,\n        COALESCE(SUM(p.total_quantity), 0) as total_portfolio_quantity\n    FROM business_base.portfolio p\n    WHERE p.customer_id = '${customer_id}'::uuid AND p.status = 'ACTIVE'\n),\ndeal_stats AS (\n    SELECT \n        COALESCE(SUM(ccs.deal_value), 0) as total_recovered_value,\n        COUNT(ccs.id) as total_deals\n    FROM business_base.collect_cash_stats ccs\n    JOIN business_base.portfolio p ON ccs.portfolio_id = p.id\n    WHERE p.customer_id = '${customer_id}'::uuid \n        AND ccs.status = 'ACTIVE' \n        AND p.status = 'ACTIVE'\n        AND ccs.created_at >= $__timeFrom()\n        AND ccs.created_at <= $__timeTo()\n),\ninteraction_stats AS (\n    SELECT \n        COUNT(CASE WHEN pi.current_status = 'SUCCEED' THEN 1 END) as successful_negotiations,\n        COUNT(CASE WHEN pi.last_interaction IS NOT NULL THEN 1 END) as total_interactions\n    FROM business_base.portfolio p\n    JOIN business_base.portfolio_item pi ON p.id = pi.portfolio_id\n    WHERE p.customer_id = '${customer_id}'::uuid \n        AND p.status = 'ACTIVE' \n        AND pi.status = 'ACTIVE'\n        AND pi.created_at >= $__timeFrom()\n        AND pi.created_at <= $__timeTo()\n)\nSELECT \n    ps.total_portfolios as \"Total Portfolios\",\n    ps.total_portfolio_quantity as \"Total Portfolio Quantity\",\n    ROUND(ds.total_recovered_value / 100.0, 2) as \"Total Recovered Value (R$)\",\n    ds.total_deals as \"Total Deals\",\n    CASE \n        WHEN is_stats.successful_negotiations > 0 \n        THEN ROUND((ds.total_recovered_value / is_stats.successful_negotiations) / 100.0, 2)\n        ELSE 0 \n    END as \"Average Ticket Value (R$)\",\n    is_stats.successful_negotiations as \"Successful Negotiations\",\n    is_stats.total_interactions as \"Total Interactions\",\n    CASE \n        WHEN is_stats.total_interactions > 0 \n        THEN ROUND((is_stats.successful_negotiations::decimal / is_stats.total_interactions::decimal) * 100, 2)\n        ELSE 0 \n    END as \"Success Rate (%)\"\nFROM portfolio_stats ps, deal_stats ds, interaction_stats is_stats;", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Recovered Value (R$)"}, "properties": [{"id": "unit", "value": "currencyBRL"}, {"id": "color", "value": {"mode": "fixed", "fixedColor": "green"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Average Ticket Value (R$)"}, "properties": [{"id": "unit", "value": "currencyBRL"}, {"id": "color", "value": {"mode": "fixed", "fixedColor": "blue"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Success Rate (%)"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "color", "value": {"mode": "fixed", "fixedColor": "orange"}}]}]}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "value", "graphMode": "area", "justifyMode": "auto"}}, {"id": 2, "title": "💰 Deal Value by Portfolio", "type": "table", "gridPos": {"h": 10, "w": 12, "x": 0, "y": 8}, "datasource": {"type": "postgres", "uid": "transcendence-postgresql-datasource"}, "targets": [{"rawSql": "SELECT \n    p.name as \"Portfolio Name\",\n    ROUND(COALESCE(SUM(ccs.deal_value), 0) / 100.0, 2) as \"Total Deal Value (R$)\",\n    COUNT(ccs.id) as \"Deal Count\"\nFROM business_base.portfolio p\nLEFT JOIN business_base.collect_cash_stats ccs ON p.id = ccs.portfolio_id \n    AND ccs.status = 'ACTIVE'\n    AND ccs.created_at >= $__timeFrom()\n    AND ccs.created_at <= $__timeTo()\nWHERE p.customer_id = '${customer_id}'::uuid\n    AND p.status = 'ACTIVE'\nGROUP BY p.id, p.name\nORDER BY \"Total Deal Value (R$)\" DESC;", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "auto"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Deal Value (R$)"}, "properties": [{"id": "unit", "value": "currencyBRL"}, {"id": "custom.displayMode", "value": "color-background"}]}]}}, {"id": 3, "title": "📈 Daily Recovered Value", "type": "timeseries", "gridPos": {"h": 10, "w": 12, "x": 12, "y": 8}, "datasource": {"type": "postgres", "uid": "transcendence-postgresql-datasource"}, "targets": [{"rawSql": "SELECT \n    DATE(ccs.created_at) as time,\n    ROUND(COALESCE(SUM(ccs.deal_value), 0) / 100.0, 2) as \"Daily Recovered Value (R$)\"\nFROM business_base.collect_cash_stats ccs\nJOIN business_base.portfolio p ON ccs.portfolio_id = p.id\nWHERE p.customer_id = '${customer_id}'::uuid\n    AND ccs.status = 'ACTIVE'\n    AND p.status = 'ACTIVE'\n    AND ccs.created_at >= $__timeFrom()\n    AND ccs.created_at <= $__timeTo()\nGROUP BY DATE(ccs.created_at)\nORDER BY time;", "format": "time_series", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "currencyBRL", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 2, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "auto", "pointSize": 5}}}}]}}