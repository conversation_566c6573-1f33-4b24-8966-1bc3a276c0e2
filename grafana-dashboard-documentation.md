# 📊 Complete Business Analytics Grafana Dashboard

## 🎯 Overview

This comprehensive Grafana dashboard provides complete business analytics for all 21 customers using the custom SQL views we created. The dashboard features dynamic filtering, real-time metrics, and comprehensive visualizations.

## 🏗️ Dashboard Structure

### **Dashboard Configuration:**
- **Title**: Complete Business Analytics Dashboard - All Customers
- **Default Time Range**: Last 1 day (matches our view fallback)
- **Refresh Rate**: 5 minutes
- **Theme**: Dark mode
- **Schema Version**: 39 (Latest Grafana)

### **Variables & Filters:**

#### 1. **Customer Filter** (`$customer_filter`)
- **Type**: Multi-select query variable
- **Query**: `SELECT DISTINCT customer_id as __value, customer_name as __text FROM v_all_customers_kpis_complete ORDER BY customer_name`
- **Options**: All customers + "All" option
- **Default**: All customers selected

#### 2. **Date Range** (`$date_range`)
- **Type**: Custom variable
- **Options**: 1 day, 3 days, 7 days, 15 days, 30 days
- **Default**: 1 day (matches view fallback)

## 📊 Panel Breakdown

### **Panel 1: 📊 Customer Overview - Top Performers**
- **Type**: Table
- **Position**: Top row (24 width, 10 height)
- **Purpose**: Complete customer overview with all KPIs
- **Features**:
  - Color-coded recovered value (heat map)
  - Color-coded success rate (heat map)
  - Currency formatting for Brazilian Real
  - Sortable by recovered value
  - Top 20 customers displayed

**Query Used:**
```sql
SELECT 
    customer_name as "Customer",
    total_portfolios as "Portfolios",
    total_portfolio_quantity as "Total Items",
    ROUND(total_recovered_value_brl, 2) as "Recovered Value (R$)",
    total_deals as "Deals",
    ROUND(average_ticket_value_brl, 2) as "Avg Ticket (R$)",
    successful_negotiations as "Successful",
    total_interactions as "Interactions",
    ROUND(success_rate_percentage, 2) as "Success Rate (%)"
FROM v_all_customers_kpis_complete
WHERE ($customer_filter = 'All' OR customer_id::text = ANY(string_to_array('$customer_filter', ',')))
ORDER BY total_recovered_value_brl DESC
LIMIT 20;
```

### **Panel 2: 💰 Total Metrics Summary**
- **Type**: Stat panel
- **Position**: Second row (24 width, 8 height)
- **Purpose**: Aggregated metrics across all selected customers
- **Features**:
  - Total portfolios, items, recovered value
  - Average metrics calculations
  - Color-coded stat cards
  - Currency formatting

**Query Used:**
```sql
SELECT 
    SUM(total_portfolios) as "Total Portfolios",
    SUM(total_portfolio_quantity) as "Total Items",
    ROUND(SUM(total_recovered_value_brl), 2) as "Total Recovered (R$)",
    SUM(total_deals) as "Total Deals",
    ROUND(AVG(average_ticket_value_brl), 2) as "Avg Ticket (R$)",
    SUM(successful_negotiations) as "Total Successful",
    SUM(total_interactions) as "Total Interactions",
    ROUND(AVG(success_rate_percentage), 2) as "Avg Success Rate (%)"
FROM v_all_customers_kpis_complete
WHERE ($customer_filter = 'All' OR customer_id::text = ANY(string_to_array('$customer_filter', ',')));
```

### **Panel 3: 📈 Top 10 Customers by Recovered Value**
- **Type**: Horizontal bar chart
- **Position**: Third row left (12 width, 10 height)
- **Purpose**: Visual ranking of top performing customers
- **Features**:
  - Horizontal bars for better readability
  - Currency formatting
  - Color-coded bars
  - Top 10 limit

### **Panel 4: 🎯 Success Rate Comparison**
- **Type**: Horizontal bar chart
- **Position**: Third row right (12 width, 10 height)
- **Purpose**: Compare success rates across customers
- **Features**:
  - Percentage formatting (0-100%)
  - Color thresholds (Red: 0-30%, Yellow: 30-60%, Green: 60%+)
  - Only shows customers with interactions

### **Panel 5: 💼 Daily Recovered Value Trend**
- **Type**: Time series
- **Position**: Fourth row left (12 width, 8 height)
- **Purpose**: Show daily recovery trends
- **Features**:
  - Uses `get_daily_recovered_value_by_customer()` function
  - Dynamic date range based on `$date_range` variable
  - Currency formatting
  - Line chart with fill

**Query Used:**
```sql
SELECT 
    recovery_date as time,
    daily_recovered_value as "Daily Recovered (R$)"
FROM get_daily_recovered_value_by_customer(
    CASE WHEN '$customer_filter' = 'All' THEN '613d0cdd-e8fb-45d0-b9b7-6ce351fb8083'::uuid 
         ELSE '$customer_filter'::uuid END,
    (CURRENT_DATE - INTERVAL '$date_range')::date,
    CURRENT_DATE::date
)
ORDER BY recovery_date;
```

### **Panel 6: 🥧 Customer Distribution by Recovered Value**
- **Type**: Pie chart
- **Position**: Fourth row right (12 width, 8 height)
- **Purpose**: Show proportional distribution of recovered values
- **Features**:
  - Top 8 customers
  - Percentage and value display
  - Right-side legend
  - Currency formatting

### **Panel 7: 📋 Detailed Customer Analytics**
- **Type**: Table
- **Position**: Bottom row (24 width, 12 height)
- **Purpose**: Comprehensive detailed view with performance indicators
- **Features**:
  - All customer metrics
  - Performance indicators (🟢 Excellent, 🟡 Good, 🟠 Average, 🔴 Needs Improvement)
  - Color-coded backgrounds for key metrics
  - Sortable columns
  - Full customer list

**Query Used:**
```sql
SELECT 
    customer_name as "Customer",
    total_portfolios as "Portfolios",
    total_portfolio_quantity as "Items",
    ROUND(total_recovered_value_brl, 2) as "Recovered (R$)",
    total_deals as "Deals",
    ROUND(average_ticket_value_brl, 2) as "Avg Ticket (R$)",
    successful_negotiations as "Successful",
    total_interactions as "Interactions",
    ROUND(success_rate_percentage, 2) as "Success Rate (%)",
    CASE 
        WHEN success_rate_percentage >= 70 THEN '🟢 Excellent'
        WHEN success_rate_percentage >= 50 THEN '🟡 Good'
        WHEN success_rate_percentage >= 30 THEN '🟠 Average'
        ELSE '🔴 Needs Improvement'
    END as "Performance"
FROM v_all_customers_kpis_complete
WHERE ($customer_filter = 'All' OR customer_id::text = ANY(string_to_array('$customer_filter', ',')))
ORDER BY total_recovered_value_brl DESC;
```

## 🎨 Visual Features

### **Color Schemes:**
- **Success Rate**: Red (0-30%) → Yellow (30-60%) → Green (60%+)
- **Recovered Value**: Continuous Green-Yellow-Red heat map
- **Performance Indicators**: Emoji-based visual indicators
- **Charts**: Grafana's classic palette for consistency

### **Formatting:**
- **Currency**: Brazilian Real (R$) with 2 decimal places
- **Percentages**: 0-100% range with 2 decimal places
- **Numbers**: Standard formatting with thousands separators

## 🔧 Technical Implementation

### **Data Sources:**
- **Primary**: PostgreSQL connection to business database
- **Views Used**: 
  - `v_all_customers_kpis_complete` (main consolidated view)
  - `get_daily_recovered_value_by_customer()` (function for daily trends)

### **Performance Optimizations:**
- Uses pre-calculated views instead of complex joins
- Limits result sets (TOP 10, TOP 20)
- Efficient filtering with customer selection
- Proper indexing on underlying tables

### **Dynamic Features:**
- Customer multi-select filtering
- Date range selection
- Real-time data updates (5-minute refresh)
- Responsive layout

## 🚀 Installation & Setup

### **Prerequisites:**
1. All customer analytics views installed (from previous files)
2. PostgreSQL data source configured in Grafana
3. Proper database permissions for view access

### **Installation Steps:**
1. Import the `grafana-business-analytics-dashboard.json` file
2. Configure the PostgreSQL data source
3. Verify variable queries work correctly
4. Test panel data loading
5. Adjust refresh rates as needed

### **Data Source Configuration:**
```yaml
datasource:
  type: postgres
  url: your-postgres-host:5432
  database: your-database-name
  user: grafana_reader
  sslmode: require
```

## 📈 Usage Examples

### **Common Use Cases:**

1. **Executive Overview**: Use Panel 1 & 2 for high-level KPIs
2. **Customer Comparison**: Use Panels 3 & 4 for competitive analysis
3. **Trend Analysis**: Use Panel 5 for daily performance tracking
4. **Detailed Analysis**: Use Panel 7 for comprehensive customer review

### **Filter Combinations:**
- **All Customers**: Get complete business overview
- **Top Performers**: Select high-value customers only
- **Specific Segments**: Filter by customer type or region
- **Time Periods**: Adjust date range for different analysis periods

## 🔍 Troubleshooting

### **Common Issues:**
1. **No Data**: Check view installation and data source connection
2. **Slow Loading**: Verify database indexes and query performance
3. **Filter Issues**: Ensure customer IDs match between variables and queries
4. **Currency Display**: Verify Brazilian Real formatting in field overrides

### **Performance Tips:**
1. Use shorter time ranges for faster loading
2. Limit customer selection for detailed analysis
3. Monitor database query performance
4. Consider caching for frequently accessed data

This dashboard provides a complete, production-ready solution for monitoring business analytics across all customers with dynamic filtering and comprehensive visualizations!
