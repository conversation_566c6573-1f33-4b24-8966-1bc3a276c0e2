version: '3.8'

services:
  grafana:
    image: grafana/grafana-enterprise:latest
    container_name: business-analytics-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      # Security settings
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123  # Change this!
      - GF_SECURITY_SECRET_KEY=your-secret-key-here  # Change this!
      
      # Server settings
      - GF_SERVER_ROOT_URL=http://localhost:3000
      - GF_SERVER_SERVE_FROM_SUB_PATH=false
      
      # Database settings (optional - uses SQLite by default)
      - GF_DATABASE_TYPE=postgres
      - GF_DATABASE_HOST=postgres:5432
      - GF_DATABASE_NAME=grafana
      - GF_DATABASE_USER=grafana
      - GF_DATABASE_PASSWORD=grafana_password
      
      # Analytics settings
      - GF_ANALYTICS_REPORTING_ENABLED=false
      - GF_ANALYTICS_CHECK_FOR_UPDATES=false
      
      # Logging
      - GF_LOG_LEVEL=info
      
      # Plugins
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
      
      # SMTP settings (optional)
      # - GF_SMTP_ENABLED=true
      # - GF_SMTP_HOST=smtp.gmail.com:587
      # - GF_SMTP_USER=<EMAIL>
      # - GF_SMTP_PASSWORD=your-app-password
      # - GF_SMTP_FROM_ADDRESS=<EMAIL>
      
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - grafana-network
    depends_on:
      - postgres

  postgres:
    image: postgres:15-alpine
    container_name: grafana-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=grafana
      - POSTGRES_USER=grafana
      - POSTGRES_PASSWORD=grafana_password
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - grafana-network
    ports:
      - "5432:5432"  # Optional: expose for external access

  # Optional: Redis for caching
  redis:
    image: redis:7-alpine
    container_name: grafana-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - grafana-network
    ports:
      - "6379:6379"  # Optional: expose for external access

volumes:
  grafana-storage:
    driver: local
  postgres-data:
    driver: local
  redis-data:
    driver: local

networks:
  grafana-network:
    driver: bridge

---
# Additional configuration files structure:
# 
# grafana/
# ├── provisioning/
# │   ├── datasources/
# │   │   └── postgres.yml
# │   └── dashboards/
# │       └── dashboard.yml
# ├── dashboards/
# │   └── business-analytics-dashboard.json
# └── init-scripts/
#     └── init-grafana-user.sql
