# 📊 Customer Business Analytics Views - Documentação

## 🎯 Vis<PERSON>eral

<PERSON>ste conjunto de views SQL fornece análises de negócios específicas para cada customer, com filtros dinâmicos de data e fallback padrão de 1 dia (últimas 24 horas).

## 📋 Lista de Customers

| Customer ID | Nome do Customer | View Name |
|-------------|------------------|-----------|
| `613d0cdd-e8fb-45d0-b9b7-6ce351fb8083` | Innova Tech | `v_innova_tech_kpis` |
| `de642fea-ee38-43d9-9b2d-d0c2c4f329b9` | Pherfil | `v_pherfil_kpis` |
| `edbf5af7-2eb7-46ed-92c2-d867758adff9` | Prod Hiring | `v_prod_hiring_kpis` |
| `7ea370bb-854d-451c-bebd-43d15b8e20c4` | Stone | `v_stone_kpis` |
| `4cd6d515-2604-4c2c-adad-435acbef1f5c` | Dev Business | `v_dev_business_kpis` |
| `0406924c-655a-4af1-8658-2b4acb349d4b` | VersaCredi | `v_versacredi_kpis` |
| `1e850c99-5354-42d2-9b83-4aca2ff856d1` | Intro Hiring Talk Demo | `v_intro_hiring_talk_demo_kpis` |
| `f4616a0e-a830-4f92-9479-d345a55ee297` | Colina | `v_colina_kpis` |
| `a66ea713-1f51-4209-9b1c-5a9282ac52ce` | CIandT | `v_ciandt_kpis` |
| `733fdc70-7064-4c86-b0db-52e15264c027` | CDL Caxias | `v_cdl_caxias_kpis` |
| `2414686d-27fa-4d83-9fe8-8d25ad40036f` | Grupo Pedrotti | `v_grupo_pedrotti_kpis` |
| `3b1b5702-8077-4dda-8d8c-ee17ff1dd139` | Cia de Talentos | `v_cia_de_talentos_kpis` |
| `ca0792d2-b9e9-440f-a186-b0b2b4c55b54` | SAF | `v_saf_kpis` |
| `42218f18-b2d3-45f9-ab2e-3ddb98288d7b` | NEF | `v_nef_kpis` |
| `67a3de20-c6aa-485d-b58e-f5f2d646eead` | DigAI Staging | `v_digai_staging_kpis` |
| `81316626-4643-48ee-9c77-db01982c7778` | Raiz Educação | `v_raiz_educacao_kpis` |
| `82283b8d-b22c-4932-887c-3881f11203f7` | TMB Educação | `v_tmb_educacao_kpis` |
| `03fa7ef1-e76e-4b6a-a5ed-ca1cee2c01a5` | Matrix Energia | `v_matrix_energia_kpis` |
| `8ff83756-1c59-45c7-b815-f28c7b0f1a5a` | Leve Saude | `v_leve_saude_kpis` |
| `c26251e9-202b-4b4d-af28-308c44226e9f` | Góes Nicoladelli | `v_goes_nicoladelli_kpis` |
| `eb6fc2f3-c635-4047-92de-584666ab110c` | Uni Internet | `v_uni_internet_kpis` |

## 🔧 Estrutura das Views

### Campos Retornados

Cada view retorna os seguintes campos:

```sql
- customer_name              -- Nome do customer
- customer_id                -- UUID do customer
- total_portfolios           -- Total de portfolios ativos
- total_portfolio_quantity   -- Soma da quantidade total de itens
- total_recovered_value_brl  -- Valor total recuperado em R$ (dividido por 100)
- total_deals               -- Total de deals/negociações
- average_ticket_value_brl  -- Valor médio do ticket em R$
- successful_negotiations   -- Negociações bem-sucedidas
- total_interactions        -- Total de interações
- success_rate_percentage   -- Taxa de sucesso em percentual
```

### Filtro de Data Dinâmico

A função `get_date_filter()` fornece filtros dinâmicos:
- **Default**: Últimas 24 horas (1 dia)
- **Parâmetros**: `start_date_param`, `end_date_param`
- **Fallback**: Se não informado, usa `CURRENT_DATE - INTERVAL '1 day'` até `CURRENT_DATE + INTERVAL '1 day'`

## 📊 Exemplos de Uso

### 1. Consultar KPIs de um Customer Específico

```sql
-- KPIs da Innova Tech (último dia)
SELECT * FROM v_innova_tech_kpis;

-- KPIs da Stone (último dia)
SELECT * FROM v_stone_kpis;
```

### 2. Consultar Todos os Customers

```sql
-- Todos os customers ordenados por valor recuperado
SELECT * FROM v_all_customers_kpis_complete 
ORDER BY total_recovered_value_brl DESC;

-- Top 5 customers por valor recuperado
SELECT 
    customer_name,
    total_recovered_value_brl,
    success_rate_percentage,
    total_deals
FROM v_all_customers_kpis_complete 
ORDER BY total_recovered_value_brl DESC 
LIMIT 5;
```

### 3. Análises Específicas

```sql
-- Customers com taxa de sucesso acima de 50%
SELECT 
    customer_name,
    success_rate_percentage,
    successful_negotiations,
    total_interactions
FROM v_all_customers_kpis_complete 
WHERE success_rate_percentage > 50
ORDER BY success_rate_percentage DESC;

-- Customers com maior ticket médio
SELECT 
    customer_name,
    average_ticket_value_brl,
    successful_negotiations
FROM v_all_customers_kpis_complete 
WHERE successful_negotiations > 0
ORDER BY average_ticket_value_brl DESC
LIMIT 10;

-- Resumo consolidado
SELECT 
    COUNT(*) as total_customers,
    SUM(total_recovered_value_brl) as total_recovered_all,
    AVG(success_rate_percentage) as avg_success_rate,
    SUM(successful_negotiations) as total_successful_negotiations
FROM v_all_customers_kpis_complete;
```

### 4. Função para Valor Recuperado Diário

```sql
-- Valor recuperado diário da Stone (Janeiro 2024)
SELECT * FROM get_daily_recovered_value_by_customer(
    '7ea370bb-854d-451c-bebd-43d15b8e20c4'::uuid,
    '2024-01-01'::date,
    '2024-01-31'::date
);

-- Valor recuperado diário da Innova Tech (última semana)
SELECT * FROM get_daily_recovered_value_by_customer(
    '613d0cdd-e8fb-45d0-b9b7-6ce351fb8083'::uuid,
    (CURRENT_DATE - INTERVAL '7 days')::date,
    CURRENT_DATE::date
);
```

## 🚀 Instalação

### Ordem de Execução dos Arquivos

```bash
# 1. Função auxiliar + primeiros 6 customers
\i customer-business-analytics-views.sql

# 2. Customers 7-10
\i customer-business-analytics-views-part2.sql

# 3. Customers 11-14
\i customer-business-analytics-views-part3.sql

# 4. Customers 15-17 + view consolidada
\i customer-business-analytics-views-final.sql

# 5. Customers 18-21 + view final completa
\i customer-business-analytics-remaining.sql
```

### Script de Instalação Única

```sql
-- Instalar todas as views de uma vez
DO $$
BEGIN
    -- Executar todos os arquivos em sequência
    RAISE NOTICE 'Instalando Customer Business Analytics Views...';
    
    -- As views serão criadas pelos arquivos SQL individuais
    RAISE NOTICE 'Instalação concluída com sucesso!';
END $$;
```

## ⚡ Performance

### Índices Recomendados

```sql
-- Índices para otimizar as consultas
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_portfolio_customer_status 
ON business_base.portfolio (customer_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_collect_cash_stats_portfolio_status_date 
ON business_base.collect_cash_stats (portfolio_id, status, created_at);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_portfolio_item_portfolio_status_current 
ON business_base.portfolio_item (portfolio_id, status, current_status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_portfolio_item_interaction 
ON business_base.portfolio_item (last_interaction) 
WHERE last_interaction IS NOT NULL;
```

### Dicas de Performance

1. **Cache de Resultados**: As views podem ser materializadas para melhor performance
2. **Filtros de Data**: Use sempre filtros de data específicos quando possível
3. **Índices**: Mantenha os índices recomendados atualizados
4. **Monitoramento**: Use `EXPLAIN ANALYZE` para monitorar performance das queries

## 🔍 Troubleshooting

### Problemas Comuns

1. **View não encontrada**: Verifique se todos os arquivos foram executados na ordem correta
2. **Dados não aparecem**: Confirme se o customer_id está correto e se há dados no período
3. **Performance lenta**: Verifique se os índices recomendados estão criados
4. **Erro de função**: Certifique-se de que a função `get_date_filter()` foi criada primeiro

### Verificação de Instalação

```sql
-- Verificar se todas as views foram criadas
SELECT schemaname, viewname 
FROM pg_views 
WHERE viewname LIKE 'v_%_kpis' 
ORDER BY viewname;

-- Testar uma view específica
SELECT customer_name, total_portfolios 
FROM v_innova_tech_kpis;

-- Verificar função auxiliar
SELECT * FROM get_date_filter();
```

## 📈 Integração com Grafana

As views podem ser facilmente integradas ao Grafana usando as queries SQL diretamente:

```sql
-- Query para dropdown de customers no Grafana
SELECT DISTINCT 
    customer_id as __value,
    customer_name as __text
FROM v_all_customers_kpis_complete 
ORDER BY customer_name;

-- Query para métricas principais
SELECT 
    customer_name,
    total_recovered_value_brl,
    success_rate_percentage,
    average_ticket_value_brl
FROM v_all_customers_kpis_complete 
WHERE customer_id = '$customer_id'
ORDER BY total_recovered_value_brl DESC;
```
