## Title

Fix sanitizeMessageBody truncation logic to actually reduce payload size

## Description

**Context:** The `sanitizeMessageBody` function in `src/common/utils/message-sanitization.util.ts` had a critical bug where it claimed to truncate large payloads but actually included the entire original object plus metadata, making the result larger than the original. This defeated the purpose of truncation and caused log bloat across all SQS message processing.

**Changes Made:**
1. **Fixed truncation logic**: When payload exceeds `maxSize`, now returns a summary object with metadata instead of the full payload
2. **Implemented object summary approach**: Shows object keys to provide meaningful insight into message structure while drastically reducing size
3. **Updated JSDoc documentation**: Clarified the actual behavior when truncation occurs
4. **Added comprehensive unit tests**: Created test suite covering all scenarios including edge cases

**Impact:**
- **84% size reduction** demonstrated in testing (1,223 → 201 characters for large payloads)
- **Reduces CloudWatch log storage costs** and improves query performance
- **Fixes misleading `_truncated: true` metadata** that previously indicated truncation when none occurred
- **Affects all SQS consumers and producers** that use this utility for message logging

**No deployment or migration steps required** - this is a backward-compatible bug fix.

## Type of Change

- [ ] Feature: New functionality (non-breaking)
- [ ] Improvement: Minor enhancements or refactoring
- [x] Bugfix: Non-breaking issue resolution
- [ ] Breaking Change: Alters existing behavior or APIs
- [ ] Security: Fixes vulnerabilities or improves security posture
- [ ] Deprecation: Marks features for removal
- [ ] Removal: Deletes deprecated or unused code
- [x] Logs/Monitoring: Adds or adjusts logging
- [ ] Repo Setup: Configuration, CI/CD, dependencies, etc.

## Checklist

- [x] Self-reviewed code
- [x] Added or adjusted E2E tests
- [ ] Tested in staging (for PRs to main branch)
- [x] Updated documentation (if applicable)
- [ ] Updated changelog (if necessary)

## Testing

**Unit Tests Added:**
- Created comprehensive test suite in `test/unit/common/utils/message-sanitization.util.spec.ts`
- Tests cover normal operation, sensitive field redaction, truncation behavior, edge cases, and custom parameters
- All 10 tests pass successfully

**Manual Testing:**
- Verified 84% size reduction for large payloads (1,223 → 201 characters)
- Confirmed object summary provides meaningful information about message structure
- Tested with various payload types (objects, strings, null/undefined)
- Validated backward compatibility with existing usage patterns

More information is available in [Confluence](https://edutalent.atlassian.net/wiki/spaces/EDUTALENT1/pages/32440321/Guidelines+for+contributions+in+our+repositories)
