# Grafana Data Source Provisioning Configuration
# Save this file as: grafana/provisioning/datasources/postgres.yml

apiVersion: 1

datasources:
  - name: PostgreSQL-Business
    type: postgres
    access: proxy
    url: your-postgres-host:5432  # Replace with your actual PostgreSQL host
    database: your-database-name  # Replace with your actual database name
    user: grafana_reader          # Use the read-only user created in setup guide
    secureJsonData:
      password: 'your-secure-password'  # Replace with actual password
    jsonData:
      sslmode: 'require'          # Use 'disable' for local development
      maxOpenConns: 10            # Maximum number of open connections
      maxIdleConns: 5             # Maximum number of idle connections
      connMaxLifetime: 14400      # Maximum connection lifetime in seconds (4 hours)
      postgresVersion: 1300       # PostgreSQL version (1300 = v13.x, 1400 = v14.x, etc.)
      timescaledb: false          # Set to true if using TimescaleDB
    isDefault: true               # Set as default data source
    editable: true                # Allow editing in UI

  # Optional: Separate data source for your application database if different
  - name: PostgreSQL-Application
    type: postgres
    access: proxy
    url: your-app-postgres-host:5432
    database: your-app-database-name
    user: grafana_app_reader
    secureJsonData:
      password: 'your-app-secure-password'
    jsonData:
      sslmode: 'require'
      maxOpenConns: 5
      maxIdleConns: 2
      connMaxLifetime: 14400
      postgresVersion: 1300
      timescaledb: false
    isDefault: false
    editable: true

---
# Dashboard Provisioning Configuration
# Save this file as: grafana/provisioning/dashboards/dashboard.yml

apiVersion: 1

providers:
  - name: 'Business Analytics'
    orgId: 1
    folder: 'Business Analytics'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards

---
# Environment-specific configurations

# Development Environment
# grafana/provisioning/datasources/postgres-dev.yml
apiVersion: 1

datasources:
  - name: PostgreSQL-Dev
    type: postgres
    access: proxy
    url: localhost:5432
    database: transcendence_dev
    user: postgres
    secureJsonData:
      password: 'dev_password'
    jsonData:
      sslmode: 'disable'          # Disable SSL for local development
      maxOpenConns: 5
      maxIdleConns: 2
      connMaxLifetime: 3600
      postgresVersion: 1500
      timescaledb: false
    isDefault: true
    editable: true

---
# Production Environment
# grafana/provisioning/datasources/postgres-prod.yml
apiVersion: 1

datasources:
  - name: PostgreSQL-Prod
    type: postgres
    access: proxy
    url: ${POSTGRES_HOST}:${POSTGRES_PORT}
    database: ${POSTGRES_DB}
    user: ${POSTGRES_USER}
    secureJsonData:
      password: '${POSTGRES_PASSWORD}'
    jsonData:
      sslmode: 'require'
      sslcert: '/etc/ssl/certs/client-cert.pem'
      sslkey: '/etc/ssl/private/client-key.pem'
      sslrootcert: '/etc/ssl/certs/ca-cert.pem'
      maxOpenConns: 20
      maxIdleConns: 10
      connMaxLifetime: 14400
      postgresVersion: 1500
      timescaledb: false
    isDefault: true
    editable: false               # Prevent editing in production

---
# Connection Pool Configuration Examples

# High-Performance Configuration (for busy systems)
jsonData:
  sslmode: 'require'
  maxOpenConns: 50              # Higher connection limit
  maxIdleConns: 25              # More idle connections
  connMaxLifetime: 7200         # 2 hours
  postgresVersion: 1500
  timescaledb: false

# Conservative Configuration (for resource-limited systems)
jsonData:
  sslmode: 'require'
  maxOpenConns: 5               # Lower connection limit
  maxIdleConns: 2               # Fewer idle connections
  connMaxLifetime: 1800         # 30 minutes
  postgresVersion: 1500
  timescaledb: false

---
# Security Best Practices

# 1. Use environment variables for sensitive data
datasources:
  - name: PostgreSQL-Secure
    type: postgres
    access: proxy
    url: ${DB_HOST}:${DB_PORT}
    database: ${DB_NAME}
    user: ${DB_USER}
    secureJsonData:
      password: '${DB_PASSWORD}'

# 2. Use dedicated read-only user
# CREATE USER grafana_reader WITH PASSWORD 'secure_random_password';
# GRANT USAGE ON SCHEMA business_base TO grafana_reader;
# GRANT SELECT ON ALL TABLES IN SCHEMA business_base TO grafana_reader;

# 3. Enable SSL/TLS in production
jsonData:
  sslmode: 'require'            # or 'verify-full' for maximum security
  sslcert: '/path/to/client-cert.pem'
  sslkey: '/path/to/client-key.pem'
  sslrootcert: '/path/to/ca-cert.pem'

# 4. Limit connection lifetime and pool size
jsonData:
  maxOpenConns: 10              # Adjust based on your database capacity
  maxIdleConns: 5               # Half of maxOpenConns is usually good
  connMaxLifetime: 14400        # 4 hours - adjust based on your needs

---
# Troubleshooting Common Issues

# Issue: "pq: SSL is not enabled on the server"
# Solution: Set sslmode to 'disable' for local development
jsonData:
  sslmode: 'disable'

# Issue: "connection refused"
# Solution: Check host, port, and network connectivity
# - Verify PostgreSQL is running
# - Check firewall settings
# - Ensure PostgreSQL accepts connections from Grafana host

# Issue: "password authentication failed"
# Solution: Verify credentials and user permissions
# - Check username and password
# - Ensure user has necessary permissions
# - Verify pg_hba.conf allows connections

# Issue: "database does not exist"
# Solution: Verify database name and user access
# - Check database name spelling
# - Ensure user has access to the database
# - Verify database exists

# Issue: "too many connections"
# Solution: Reduce connection pool size
jsonData:
  maxOpenConns: 5               # Reduce from default
  maxIdleConns: 2               # Reduce accordingly

---
# Performance Optimization

# For high-traffic dashboards:
jsonData:
  maxOpenConns: 25              # Increase connection pool
  maxIdleConns: 10              # Keep more idle connections
  connMaxLifetime: 7200         # Longer connection lifetime

# For read-heavy workloads, consider:
# 1. Using read replicas
# 2. Implementing connection pooling (PgBouncer)
# 3. Adding query result caching
# 4. Optimizing dashboard refresh intervals

# Query timeout settings (if supported):
jsonData:
  queryTimeout: '30s'           # Timeout for individual queries
  connectionTimeout: '10s'      # Connection establishment timeout
