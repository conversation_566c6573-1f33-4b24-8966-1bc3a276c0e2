# Grafana Business Analytics Dashboard Setup Guide

## 📊 Dashboard Overview

This Grafana dashboard provides comprehensive business analytics for your portfolio management system, featuring:

- **Key Performance Indicators (KPIs)** - Total recovered value, average ticket, success rate
- **Time Series Charts** - Daily trends for recovered value, imports, success rate
- **Distribution Charts** - Portfolio status breakdown, deal value by portfolio
- **Performance Tables** - Detailed portfolio performance metrics

## 🔧 Prerequisites

### 1. Grafana Installation
```bash
# Using Docker
docker run -d -p 3000:3000 --name=grafana grafana/grafana-enterprise

# Or using package manager (Ubuntu/Debian)
sudo apt-get install -y software-properties-common
sudo add-apt-repository "deb https://packages.grafana.com/oss/deb stable main"
sudo apt-get update
sudo apt-get install grafana
```

### 2. PostgreSQL Data Source Configuration

#### Connection Settings:
- **Host**: `your-postgres-host:5432`
- **Database**: `your-database-name`
- **User**: `grafana-readonly-user` (recommended)
- **SSL Mode**: `require` (for production)

#### Create Read-Only User (Recommended):
```sql
-- Create dedicated Grafana user
CREATE USER grafana_reader WITH PASSWORD 'secure_password';

-- Grant schema access
GRANT USAGE ON SCHEMA business_base TO grafana_reader;
GRANT USAGE ON SCHEMA message_hub TO grafana_reader;

-- Grant table access
GRANT SELECT ON ALL TABLES IN SCHEMA business_base TO grafana_reader;
GRANT SELECT ON ALL TABLES IN SCHEMA message_hub TO grafana_reader;

-- Grant access to future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA business_base GRANT SELECT ON TABLES TO grafana_reader;
ALTER DEFAULT PRIVILEGES IN SCHEMA message_hub GRANT SELECT ON TABLES TO grafana_reader;
```

## 📥 Dashboard Installation

### Method 1: Import JSON File
1. Open Grafana UI (`http://localhost:3000`)
2. Navigate to **Dashboards** → **Import**
3. Upload `grafana-business-analytics-dashboard.json`
4. Select your PostgreSQL data source
5. Click **Import**

### Method 2: Manual Creation
1. Create new dashboard
2. Copy panel configurations from the JSON file
3. Configure each panel with the provided SQL queries

## 🎛️ Dashboard Configuration

### Variables Configuration

The dashboard uses a customer filter variable:

```sql
-- Customer Selection Query
SELECT DISTINCT 
    customer_id as __value,
    name as __text
FROM business_base.customer 
WHERE status = 'ACTIVE' 
ORDER BY name;
```

### Time Range Settings
- **Default Range**: Last 30 days
- **Refresh Intervals**: 5s, 10s, 30s, 1m, 5m, 15m, 30m, 1h, 2h, 1d
- **Auto Refresh**: 5 minutes (configurable)

## 📈 Panel Descriptions

### 1. Key Performance Indicators (KPIs)
**Type**: Stat Panel  
**Metrics**: 
- Total Portfolios
- Total Portfolio Quantity  
- Total Recovered Value (R$)
- Total Deals
- Average Ticket Value (R$)
- Successful Negotiations
- Total Interactions
- Success Rate (%)

### 2. Daily Recovered Value
**Type**: Time Series  
**Description**: Shows daily deal value recovery trends over time  
**Unit**: Brazilian Real (BRL)

### 3. Daily Portfolio Imports
**Type**: Time Series (Bar Chart)  
**Description**: Number of portfolios imported per day  
**Unit**: Count

### 4. Success Rate Trend
**Type**: Time Series  
**Description**: Daily success rate percentage trend  
**Unit**: Percentage (0-100%)

### 5. Deal Value by Portfolio
**Type**: Pie Chart  
**Description**: Distribution of deal values across portfolios  
**Limit**: Top 10 portfolios

### 6. Daily First Messages Sent
**Type**: Time Series (Bar Chart)  
**Description**: Number of first messages sent per day  
**Unit**: Count

### 7. Successful Negotiations with Interactions
**Type**: Time Series  
**Description**: Daily count of successful negotiations that had interactions  
**Unit**: Count

### 8. Portfolio Status Distribution
**Type**: Donut Chart  
**Description**: Distribution of portfolio item statuses  
**Statuses**: SUCCEED, IN_PROGRESS, PENDING, FAILED, etc.

### 9. Average Ticket Value Trend
**Type**: Time Series  
**Description**: Daily average ticket value trend  
**Unit**: Brazilian Real (BRL)

### 10. Portfolio Performance Table
**Type**: Table  
**Columns**:
- Portfolio Name
- Total Items
- Successful Count
- With Interactions Count
- Total Value (R$)
- Average Ticket (R$)
- Success Rate (%)

## 🔍 Query Optimization

### Recommended Indexes
```sql
-- Composite indexes for better performance
CREATE INDEX CONCURRENTLY idx_collect_cash_stats_customer_date_status 
ON business_base.collect_cash_stats (customer_id, created_at, status);

CREATE INDEX CONCURRENTLY idx_portfolio_customer_status_date 
ON business_base.portfolio (customer_id, status, created_at);

CREATE INDEX CONCURRENTLY idx_portfolio_item_portfolio_status_current 
ON business_base.portfolio_item (portfolio_id, status, current_status);

CREATE INDEX CONCURRENTLY idx_portfolio_item_interaction_date 
ON business_base.portfolio_item (last_interaction, created_at) 
WHERE last_interaction IS NOT NULL;

CREATE INDEX CONCURRENTLY idx_outgoing_message_customer_first_sent 
ON message_hub.outgoing_message (customer_id, is_first_message, sent, sent_at) 
WHERE is_first_message = true AND sent = true;
```

### Query Performance Tips
1. **Use time range filters** - Always apply time filters to limit data scope
2. **Customer filtering** - Filter by customer_id early in queries
3. **Status filtering** - Always filter by status = 'ACTIVE'
4. **Limit results** - Use LIMIT for large result sets
5. **Monitor query performance** - Use EXPLAIN ANALYZE for slow queries

## 🚀 Advanced Configuration

### Alerting Setup
```yaml
# Example alert rule for low success rate
- alert: LowSuccessRate
  expr: success_rate_percentage < 10
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "Success rate below 10% for customer {{ $labels.customer_id }}"
```

### Custom Variables
Add additional variables for more granular filtering:

```sql
-- Portfolio Selection
SELECT DISTINCT 
    p.id as __value,
    p.name as __text
FROM business_base.portfolio p
WHERE p.customer_id = '$customer_id' 
    AND p.status = 'ACTIVE'
ORDER BY p.name;

-- Date Range Presets
SELECT 
    'Last 7 days' as __text,
    'now-7d' as __value
UNION ALL
SELECT 
    'Last 30 days' as __text,
    'now-30d' as __value
UNION ALL
SELECT 
    'Last 90 days' as __text,
    'now-90d' as __value;
```

### Permissions & Security
1. **Dashboard Permissions**: Set appropriate view/edit permissions
2. **Data Source Security**: Use read-only database user
3. **Row-Level Security**: Implement customer-based data isolation
4. **SSL/TLS**: Enable encrypted connections to database

## 🔧 Troubleshooting

### Common Issues

1. **No Data Showing**
   - Check data source connection
   - Verify customer_id variable selection
   - Confirm time range includes data

2. **Slow Query Performance**
   - Add recommended indexes
   - Reduce time range
   - Check query execution plans

3. **Permission Errors**
   - Verify database user permissions
   - Check schema access grants
   - Confirm table-level permissions

4. **Variable Not Working**
   - Check variable query syntax
   - Verify data source selection
   - Test variable query independently

### Performance Monitoring
```sql
-- Monitor slow queries
SELECT 
    query,
    mean_exec_time,
    calls,
    total_exec_time
FROM pg_stat_statements 
WHERE query LIKE '%business_base%'
ORDER BY mean_exec_time DESC
LIMIT 10;
```

## 📚 Additional Resources

- [Grafana Documentation](https://grafana.com/docs/)
- [PostgreSQL Data Source](https://grafana.com/docs/grafana/latest/datasources/postgres/)
- [Dashboard Best Practices](https://grafana.com/docs/grafana/latest/best-practices/)
- [Query Optimization Guide](https://grafana.com/docs/grafana/latest/panels/query-a-data-source/)
