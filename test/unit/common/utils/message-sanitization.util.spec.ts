import {
  sanitizeMessageBody,
  redactSensitiveFields,
  sanitizeBatchMessages,
  DEFAULT_SENSITIVE_FIELDS,
  DEFAULT_MAX_MESSAGE_SIZE,
} from '@common/utils/message-sanitization.util';

describe('Message Sanitization Utility', () => {
  describe('sanitizeMessageBody', () => {
    it('should return the original object when under size limit', () => {
      const smallMessage = { id: '123', message: 'Hello World' };
      const result = sanitizeMessageBody(smallMessage);

      expect(result).toEqual(smallMessage);
      expect(result._truncated).toBeUndefined();
    });

    it('should redact sensitive fields', () => {
      const messageWithSensitiveData = {
        id: '123',
        password: 'secret123',
        token: 'abc123',
        message: 'Hello World',
      };

      const result = sanitizeMessageBody(messageWithSensitiveData);

      expect(result.id).toBe('123');
      expect(result.message).toBe('Hello World');
      expect(result.password).toBe('[REDACTED]');
      expect(result.token).toBe('[REDACTED]');
    });

    it('should truncate large payloads and return summary object', () => {
      // Create a large object that exceeds DEFAULT_MAX_MESSAGE_SIZE (1000 chars)
      const largeMessage = {
        id: '123',
        data: 'x'.repeat(1000), // This will make the JSON string > 1000 chars
        metadata: { type: 'test', version: '1.0' },
      };

      const result = sanitizeMessageBody(largeMessage);

      // Should return truncation summary instead of original object
      expect(result._truncated).toBe(true);
      expect(result._summary).toBe('Object with keys: [id, data, metadata]');
      expect(result._originalSize).toBeGreaterThan(DEFAULT_MAX_MESSAGE_SIZE);
      expect(result._maxSize).toBe(DEFAULT_MAX_MESSAGE_SIZE);
      expect(result._note).toBe('Message body truncated for logging');

      // Should NOT contain the original large data
      expect(result.data).toBeUndefined();
      expect(result.id).toBeUndefined();
      expect(result.metadata).toBeUndefined();
    });

    it('should handle non-object payloads when truncating', () => {
      const largeString = 'x'.repeat(1500);
      const result = sanitizeMessageBody(largeString);

      expect(result._truncated).toBe(true);
      expect(result._summary).toBe('string payload');
      expect(result._originalSize).toBe(1502); // Including quotes from JSON.stringify
    });

    it('should handle null and undefined inputs', () => {
      expect(sanitizeMessageBody(null)).toBeNull();
      expect(sanitizeMessageBody(undefined)).toBeUndefined();
    });

    it('should handle unparseable objects', () => {
      const circularObj: any = { name: 'test' };
      circularObj.self = circularObj; // Create circular reference

      const result = sanitizeMessageBody(circularObj);
      expect(result).toBe('[UNPARSEABLE_MESSAGE_BODY]');
    });

    it('should use custom maxSize parameter', () => {
      const message = { data: 'x'.repeat(100) };
      const customMaxSize = 50;

      const result = sanitizeMessageBody(message, DEFAULT_SENSITIVE_FIELDS, customMaxSize);

      expect(result._truncated).toBe(true);
      expect(result._maxSize).toBe(customMaxSize);
    });

    it('should use custom sensitive fields', () => {
      const message = {
        customSecret: 'secret123',
        password: 'password123',
        normalField: 'normal',
      };

      const customSensitiveFields = ['customSecret'];
      const result = sanitizeMessageBody(message, customSensitiveFields);

      expect(result.customSecret).toBe('[REDACTED]');
      expect(result.password).toBe('password123'); // Not redacted with custom fields
      expect(result.normalField).toBe('normal');
    });
  });

  describe('redactSensitiveFields', () => {
    it('should redact sensitive fields recursively', () => {
      const obj = {
        user: {
          name: 'John',
          password: 'secret',
          profile: {
            token: 'abc123',
            email: '<EMAIL>',
          },
        },
        apiKey: 'key123',
      };

      redactSensitiveFields(obj);

      expect(obj.user.name).toBe('John');
      expect(obj.user.password).toBe('[REDACTED]');
      expect(obj.user.profile.token).toBe('[REDACTED]');
      expect(obj.user.profile.email).toBe('<EMAIL>');
      expect(obj.apiKey).toBe('[REDACTED]');
    });

    it('should handle arrays', () => {
      const obj = {
        users: [
          { name: 'John', password: 'secret1' },
          { name: 'Jane', token: 'token123' },
        ],
      };

      redactSensitiveFields(obj);

      expect(obj.users[0].name).toBe('John');
      expect(obj.users[0].password).toBe('[REDACTED]');
      expect(obj.users[1].name).toBe('Jane');
      expect(obj.users[1].token).toBe('[REDACTED]');
    });
  });
});
