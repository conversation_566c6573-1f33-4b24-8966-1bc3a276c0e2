#!/bin/bash

# Business Analytics Grafana Dashboard Deployment Script
# This script sets up <PERSON><PERSON> with the business analytics dashboard

set -e  # Exit on any error

# Configuration
GRAFANA_VERSION="latest"
POSTGRES_VERSION="15-alpine"
REDIS_VERSION="7-alpine"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if required files exist
    required_files=(
        "grafana-business-analytics-dashboard.json"
        "docker-compose-grafana.yml"
        "grafana-provisioning-datasource.yml"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "Required file $file not found!"
            exit 1
        fi
    done
    
    log_success "Prerequisites check passed!"
}

# Create directory structure
create_directories() {
    log_info "Creating directory structure..."
    
    mkdir -p grafana/provisioning/datasources
    mkdir -p grafana/provisioning/dashboards
    mkdir -p grafana/dashboards
    mkdir -p init-scripts
    
    log_success "Directory structure created!"
}

# Setup configuration files
setup_configuration() {
    log_info "Setting up configuration files..."
    
    # Copy dashboard JSON
    cp grafana-business-analytics-dashboard.json grafana/dashboards/
    
    # Create datasource provisioning file
    cat > grafana/provisioning/datasources/postgres.yml << 'EOF'
apiVersion: 1

datasources:
  - name: PostgreSQL-Business
    type: postgres
    access: proxy
    url: ${POSTGRES_HOST:-localhost:5432}
    database: ${POSTGRES_DB:-transcendence}
    user: ${POSTGRES_USER:-grafana_reader}
    secureJsonData:
      password: '${POSTGRES_PASSWORD:-grafana_password}'
    jsonData:
      sslmode: '${POSTGRES_SSL_MODE:-disable}'
      maxOpenConns: 10
      maxIdleConns: 5
      connMaxLifetime: 14400
      postgresVersion: 1500
      timescaledb: false
    isDefault: true
    editable: true
EOF

    # Create dashboard provisioning file
    cat > grafana/provisioning/dashboards/dashboard.yml << 'EOF'
apiVersion: 1

providers:
  - name: 'Business Analytics'
    orgId: 1
    folder: 'Business Analytics'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
EOF

    # Create database initialization script
    cat > init-scripts/init-grafana-user.sql << 'EOF'
-- Create Grafana read-only user if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'grafana_reader') THEN
        CREATE USER grafana_reader WITH PASSWORD 'grafana_password';
    END IF;
END
$$;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA business_base TO grafana_reader;
GRANT USAGE ON SCHEMA message_hub TO grafana_reader;
GRANT SELECT ON ALL TABLES IN SCHEMA business_base TO grafana_reader;
GRANT SELECT ON ALL TABLES IN SCHEMA message_hub TO grafana_reader;
ALTER DEFAULT PRIVILEGES IN SCHEMA business_base GRANT SELECT ON TABLES TO grafana_reader;
ALTER DEFAULT PRIVILEGES IN SCHEMA message_hub GRANT SELECT ON TABLES TO grafana_reader;
EOF

    log_success "Configuration files created!"
}

# Create environment file
create_env_file() {
    log_info "Creating environment file..."
    
    cat > .env << 'EOF'
# Grafana Configuration
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin123
GRAFANA_SECRET_KEY=your-secret-key-here

# PostgreSQL Configuration (for Grafana metadata)
POSTGRES_DB=grafana
POSTGRES_USER=grafana
POSTGRES_PASSWORD=grafana_password

# Business Database Configuration (your application database)
BUSINESS_POSTGRES_HOST=localhost
BUSINESS_POSTGRES_PORT=5432
BUSINESS_POSTGRES_DB=transcendence
BUSINESS_POSTGRES_USER=grafana_reader
BUSINESS_POSTGRES_PASSWORD=your-business-db-password
BUSINESS_POSTGRES_SSL_MODE=disable

# Optional: SMTP Configuration
# SMTP_ENABLED=false
# SMTP_HOST=smtp.gmail.com:587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_FROM_ADDRESS=<EMAIL>
EOF

    log_warning "Please edit .env file with your actual database credentials!"
    log_warning "Default admin credentials: admin/admin123 - CHANGE THESE!"
}

# Deploy with Docker Compose
deploy_services() {
    log_info "Deploying Grafana services..."
    
    # Start services
    docker-compose -f docker-compose-grafana.yml up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to start..."
    sleep 30
    
    # Check if Grafana is accessible
    if curl -f http://localhost:3000/api/health &> /dev/null; then
        log_success "Grafana is running and accessible at http://localhost:3000"
    else
        log_warning "Grafana might still be starting up. Please check manually."
    fi
}

# Create recommended database indexes
create_indexes() {
    log_info "Creating recommended database indexes..."
    
    # Note: This requires connection to your business database
    log_warning "Please run the following SQL commands on your business database:"
    
    cat << 'EOF'

-- Recommended indexes for optimal dashboard performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_collect_cash_stats_customer_date_status 
ON business_base.collect_cash_stats (customer_id, created_at, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_portfolio_customer_status_date 
ON business_base.portfolio (customer_id, status, created_at);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_portfolio_item_portfolio_status_current 
ON business_base.portfolio_item (portfolio_id, status, current_status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_portfolio_item_interaction_date 
ON business_base.portfolio_item (last_interaction, created_at) 
WHERE last_interaction IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_outgoing_message_customer_first_sent 
ON message_hub.outgoing_message (customer_id, is_first_message, sent, sent_at) 
WHERE is_first_message = true AND sent = true;

EOF
}

# Display post-deployment information
show_post_deployment_info() {
    log_success "Deployment completed successfully!"
    
    echo ""
    echo "=== ACCESS INFORMATION ==="
    echo "Grafana URL: http://localhost:3000"
    echo "Default Username: admin"
    echo "Default Password: admin123"
    echo ""
    echo "=== NEXT STEPS ==="
    echo "1. Change default admin password"
    echo "2. Update .env file with your database credentials"
    echo "3. Configure data source connection in Grafana UI"
    echo "4. Run recommended database indexes (see output above)"
    echo "5. Test dashboard functionality"
    echo ""
    echo "=== USEFUL COMMANDS ==="
    echo "View logs: docker-compose -f docker-compose-grafana.yml logs -f"
    echo "Stop services: docker-compose -f docker-compose-grafana.yml down"
    echo "Restart services: docker-compose -f docker-compose-grafana.yml restart"
    echo ""
}

# Cleanup function
cleanup() {
    log_info "Cleaning up temporary files..."
    # Add cleanup logic if needed
}

# Main deployment function
main() {
    log_info "Starting Business Analytics Grafana Dashboard deployment..."
    
    check_prerequisites
    create_directories
    setup_configuration
    create_env_file
    deploy_services
    create_indexes
    show_post_deployment_info
    
    log_success "Deployment script completed!"
}

# Handle script interruption
trap cleanup EXIT

# Parse command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        log_info "Stopping Grafana services..."
        docker-compose -f docker-compose-grafana.yml down
        log_success "Services stopped!"
        ;;
    "restart")
        log_info "Restarting Grafana services..."
        docker-compose -f docker-compose-grafana.yml restart
        log_success "Services restarted!"
        ;;
    "logs")
        docker-compose -f docker-compose-grafana.yml logs -f
        ;;
    "clean")
        log_warning "This will remove all containers and volumes. Are you sure? (y/N)"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            docker-compose -f docker-compose-grafana.yml down -v
            docker system prune -f
            log_success "Cleanup completed!"
        else
            log_info "Cleanup cancelled."
        fi
        ;;
    "help")
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  deploy   - Deploy Grafana dashboard (default)"
        echo "  stop     - Stop all services"
        echo "  restart  - Restart all services"
        echo "  logs     - Show service logs"
        echo "  clean    - Remove all containers and volumes"
        echo "  help     - Show this help message"
        ;;
    *)
        log_error "Unknown command: $1"
        echo "Use '$0 help' for usage information."
        exit 1
        ;;
esac
