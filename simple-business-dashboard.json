{"dashboard": {"id": null, "title": "Simple Business Analytics", "tags": ["business", "analytics"], "style": "dark", "timezone": "browser", "refresh": "5m", "schemaVersion": 39, "version": 1, "time": {"from": "now-30d", "to": "now"}, "templating": {"list": [{"name": "customer_id", "type": "custom", "label": "Customer", "query": "613d0cdd-e8fb-45d0-b9b7-6ce351fb8083 : Innova Tech,1e2f3a4b-5c6d-7e8f-9a0b-1c2d3e4f5a6b : Matrix Energia", "refresh": 0, "includeAll": true, "multi": false, "allValue": "", "current": {"selected": false, "text": "All", "value": "$__all"}, "options": [{"text": "All", "value": "$__all", "selected": true}, {"text": "Innova Tech", "value": "613d0cdd-e8fb-45d0-b9b7-6ce351fb8083", "selected": false}, {"text": "Matrix Energia", "value": "1e2f3a4b-5c6d-7e8f-9a0b-1c2d3e4f5a6b", "selected": false}]}]}, "panels": [{"id": 1, "title": "📊 Business Summary", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "datasource": {"type": "postgres", "uid": "transcendence-postgresql-datasource"}, "targets": [{"rawSql": "SELECT \n    COUNT(DISTINCT p.id) as \"Total Portfolios\",\n    COALESCE(SUM(p.total_quantity), 0) as \"Total Portfolio Quantity\",\n    ROUND(COALESCE(SUM(ccs.deal_value), 0) / 100.0, 2) as \"Total Recovered Value (R$)\",\n    COUNT(ccs.id) as \"Total Deals\"\nFROM business_base.portfolio p\nLEFT JOIN business_base.collect_cash_stats ccs ON p.id = ccs.portfolio_id \n    AND ccs.status = 'ACTIVE'\n    AND ccs.created_at >= $__timeFrom()\n    AND ccs.created_at <= $__timeTo()\nWHERE ($customer_id = '$__all' OR p.customer_id = '${customer_id}'::uuid)\n    AND p.status = 'ACTIVE';", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Recovered Value (R$)"}, "properties": [{"id": "unit", "value": "currencyBRL"}, {"id": "color", "value": {"mode": "fixed", "fixedColor": "green"}}]}]}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "value", "graphMode": "area", "justifyMode": "auto"}}, {"id": 2, "title": "💰 Portfolio Performance", "type": "table", "gridPos": {"h": 10, "w": 24, "x": 0, "y": 8}, "datasource": {"type": "postgres", "uid": "transcendence-postgresql-datasource"}, "targets": [{"rawSql": "SELECT \n    p.name as \"Portfolio Name\",\n    ROUND(COALESCE(SUM(ccs.deal_value), 0) / 100.0, 2) as \"Total Deal Value (R$)\",\n    COUNT(ccs.id) as \"Deal Count\"\nFROM business_base.portfolio p\nLEFT JOIN business_base.collect_cash_stats ccs ON p.id = ccs.portfolio_id \n    AND ccs.status = 'ACTIVE'\n    AND ccs.created_at >= $__timeFrom()\n    AND ccs.created_at <= $__timeTo()\nWHERE ($customer_id = '$__all' OR p.customer_id = '${customer_id}'::uuid)\n    AND p.status = 'ACTIVE'\nGROUP BY p.id, p.name\nORDER BY \"Total Deal Value (R$)\" DESC\nLIMIT 10;", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "auto"}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Deal Value (R$)"}, "properties": [{"id": "unit", "value": "currencyBRL"}, {"id": "custom.displayMode", "value": "color-background"}]}]}}]}}