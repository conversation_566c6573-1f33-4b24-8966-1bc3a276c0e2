-- Business Analytics Metrics Queries
-- Based on Prisma schema: collect-cash-stats, portfolio, and portfolio-item

-- =====================================================
-- 1. TOTAL DEAL VALUE BY PORTFOLIO
-- =====================================================

-- Raw SQL Query
SELECT 
    p.id as portfolio_id,
    p.name as portfolio_name,
    COALESCE(SUM(ccs.deal_value), 0) as total_deal_value,
    COUNT(ccs.id) as deal_count
FROM business_base.portfolio p
LEFT JOIN business_base.collect_cash_stats ccs ON p.id = ccs.portfolio_id 
    AND ccs.status = 'ACTIVE'
    AND ccs.created_at >= $2::timestamp  -- startDate
    AND ccs.created_at <= $3::timestamp  -- endDate
WHERE p.customer_id = $1::uuid
    AND p.status = 'ACTIVE'
GROUP BY p.id, p.name
ORDER BY total_deal_value DESC;

-- Prisma Query (TypeScript)
/*
const totalDealValueByPortfolio = await prisma.portfolio.findMany({
  where: {
    customerId: customerId,
    status: 'ACTIVE',
  },
  select: {
    id: true,
    name: true,
    _count: {
      select: {
        collectCashStats: {
          where: {
            status: 'ACTIVE',
            createdAt: {
              gte: startDate,
              lte: endDate,
            },
          },
        },
      },
    },
  },
  // Note: Prisma doesn't support SUM in select, so you'd need a separate query
});

// Separate aggregation query for each portfolio
const dealValueSum = await prisma.collectCashStats.aggregate({
  where: {
    portfolioId: portfolioId,
    status: 'ACTIVE',
    createdAt: {
      gte: startDate,
      lte: endDate,
    },
  },
  _sum: {
    dealValue: true,
  },
});
*/

-- =====================================================
-- 2. AVERAGE TICKET VALUE
-- =====================================================

-- Raw SQL Query
SELECT 
    CASE 
        WHEN COUNT(pi.id) > 0 THEN COALESCE(SUM(ccs.deal_value), 0) / COUNT(pi.id)
        ELSE 0 
    END as average_ticket_value,
    COALESCE(SUM(ccs.deal_value), 0) as total_deal_value,
    COUNT(pi.id) as successful_negotiations_count
FROM business_base.portfolio p
LEFT JOIN business_base.portfolio_item pi ON p.id = pi.portfolio_id 
    AND pi.current_status = 'SUCCEED' 
    AND pi.status = 'ACTIVE'
LEFT JOIN business_base.collect_cash_stats ccs ON pi.id = ccs.portfolio_item_id 
    AND ccs.status = 'ACTIVE'
    AND ccs.created_at >= $2::timestamp  -- startDate
    AND ccs.created_at <= $3::timestamp  -- endDate
WHERE p.customer_id = $1::uuid
    AND p.status = 'ACTIVE';

-- =====================================================
-- 3. TOTAL PORTFOLIO QUANTITY
-- =====================================================

-- Raw SQL Query
SELECT 
    COALESCE(SUM(p.total_quantity), 0) as total_portfolio_quantity
FROM business_base.portfolio p
WHERE p.customer_id = $1::uuid
    AND p.status = 'ACTIVE';

-- Prisma Query (TypeScript)
/*
const totalQuantity = await prisma.portfolio.aggregate({
  where: {
    customerId: customerId,
    status: 'ACTIVE',
  },
  _sum: {
    totalQuantity: true,
  },
});
*/

-- =====================================================
-- 4. SUCCESSFUL NEGOTIATIONS WITH INTERACTIONS
-- =====================================================

-- Raw SQL Query - Grouped by Date
SELECT 
    DATE(pi.created_at) as date,
    COUNT(pi.id) as successful_negotiations_with_interactions
FROM business_base.portfolio p
JOIN business_base.portfolio_item pi ON p.id = pi.portfolio_id
WHERE p.customer_id = $1::uuid
    AND p.status = 'ACTIVE'
    AND pi.current_status = 'SUCCEED'
    AND pi.last_interaction IS NOT NULL
    AND pi.status = 'ACTIVE'
    AND pi.created_at >= $2::timestamp  -- startDate
    AND pi.created_at <= $3::timestamp  -- endDate
GROUP BY DATE(pi.created_at)
ORDER BY date;

-- =====================================================
-- 5. DAILY IMPORTS BY CUSTOMER (Portfolio Creation)
-- =====================================================

-- Raw SQL Query
SELECT 
    DATE(p.created_at) as import_date,
    COUNT(p.id) as portfolios_imported
FROM business_base.portfolio p
WHERE p.customer_id = $1::uuid
    AND p.status = 'ACTIVE'
    AND p.created_at >= $2::timestamp  -- startDate
    AND p.created_at <= $3::timestamp  -- endDate
GROUP BY DATE(p.created_at)
ORDER BY import_date;

-- =====================================================
-- 6. DAILY INITIATED CONTACTS (First Messages Sent)
-- =====================================================

-- Raw SQL Query (requires message_hub schema)
SELECT 
    DATE(om.sent_at) as contact_date,
    COUNT(om.id) as first_messages_sent
FROM message_hub.outgoing_message om
WHERE om.customer_id = $1::uuid
    AND om.sent = true
    AND om.is_first_message = true
    AND om.sent_at >= $2::timestamp  -- startDate
    AND om.sent_at <= $3::timestamp  -- endDate
GROUP BY DATE(om.sent_at)
ORDER BY contact_date;

-- =====================================================
-- 7. SUCCESS RATE CALCULATION
-- =====================================================

-- Raw SQL Query
SELECT 
    COUNT(CASE WHEN pi.current_status = 'SUCCEED' THEN 1 END) as successful_negotiations,
    COUNT(CASE WHEN pi.last_interaction IS NOT NULL THEN 1 END) as total_interactions,
    CASE 
        WHEN COUNT(CASE WHEN pi.last_interaction IS NOT NULL THEN 1 END) > 0 
        THEN ROUND(
            (COUNT(CASE WHEN pi.current_status = 'SUCCEED' THEN 1 END)::decimal / 
             COUNT(CASE WHEN pi.last_interaction IS NOT NULL THEN 1 END)::decimal) * 100, 2
        )
        ELSE 0 
    END as success_rate_percentage
FROM business_base.portfolio p
JOIN business_base.portfolio_item pi ON p.id = pi.portfolio_id
WHERE p.customer_id = $1::uuid
    AND p.status = 'ACTIVE'
    AND pi.status = 'ACTIVE'
    AND pi.created_at >= $2::timestamp  -- startDate
    AND pi.created_at <= $3::timestamp; -- endDate

-- =====================================================
-- 8. DAILY RECOVERED VALUE (Deal Value by Date)
-- =====================================================

-- Raw SQL Query
SELECT 
    DATE(ccs.created_at) as recovery_date,
    COALESCE(SUM(ccs.deal_value), 0) as daily_recovered_value,
    COUNT(ccs.id) as deals_count
FROM business_base.collect_cash_stats ccs
JOIN business_base.portfolio p ON ccs.portfolio_id = p.id
WHERE p.customer_id = $1::uuid
    AND ccs.status = 'ACTIVE'
    AND p.status = 'ACTIVE'
    AND ccs.created_at >= $2::timestamp  -- startDate
    AND ccs.created_at <= $3::timestamp  -- endDate
GROUP BY DATE(ccs.created_at)
ORDER BY recovery_date;

-- =====================================================
-- 9. COMPREHENSIVE DASHBOARD SUMMARY
-- =====================================================

-- Raw SQL Query - Single query for dashboard overview
WITH portfolio_stats AS (
    SELECT 
        COUNT(DISTINCT p.id) as total_portfolios,
        COALESCE(SUM(p.total_quantity), 0) as total_portfolio_quantity
    FROM business_base.portfolio p
    WHERE p.customer_id = $1::uuid AND p.status = 'ACTIVE'
),
deal_stats AS (
    SELECT 
        COALESCE(SUM(ccs.deal_value), 0) as total_recovered_value,
        COUNT(ccs.id) as total_deals
    FROM business_base.collect_cash_stats ccs
    JOIN business_base.portfolio p ON ccs.portfolio_id = p.id
    WHERE p.customer_id = $1::uuid 
        AND ccs.status = 'ACTIVE' 
        AND p.status = 'ACTIVE'
        AND ccs.created_at >= $2::timestamp 
        AND ccs.created_at <= $3::timestamp
),
interaction_stats AS (
    SELECT 
        COUNT(CASE WHEN pi.current_status = 'SUCCEED' THEN 1 END) as successful_negotiations,
        COUNT(CASE WHEN pi.last_interaction IS NOT NULL THEN 1 END) as total_interactions,
        COUNT(CASE WHEN pi.current_status = 'SUCCEED' AND pi.last_interaction IS NOT NULL THEN 1 END) as successful_with_interactions
    FROM business_base.portfolio p
    JOIN business_base.portfolio_item pi ON p.id = pi.portfolio_id
    WHERE p.customer_id = $1::uuid 
        AND p.status = 'ACTIVE' 
        AND pi.status = 'ACTIVE'
        AND pi.created_at >= $2::timestamp 
        AND pi.created_at <= $3::timestamp
)
SELECT 
    ps.total_portfolios,
    ps.total_portfolio_quantity,
    ds.total_recovered_value,
    ds.total_deals,
    CASE 
        WHEN is_stats.successful_negotiations > 0 
        THEN ds.total_recovered_value / is_stats.successful_negotiations 
        ELSE 0 
    END as average_ticket_value,
    is_stats.successful_negotiations,
    is_stats.total_interactions,
    is_stats.successful_with_interactions,
    CASE 
        WHEN is_stats.total_interactions > 0 
        THEN ROUND((is_stats.successful_negotiations::decimal / is_stats.total_interactions::decimal) * 100, 2)
        ELSE 0 
    END as success_rate_percentage
FROM portfolio_stats ps, deal_stats ds, interaction_stats is_stats;
