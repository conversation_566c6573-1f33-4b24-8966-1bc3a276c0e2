# 📊 Grafana Business Analytics Queries

Based on your `business-analytics-queries.sql` file, here are the exact queries for Grafana panels.

## 🎯 **Panel Setup Instructions**

### **Variable Setup:**
1. **Customer Variable:** `customer_id` (dropdown with your 21 customer UUIDs)
2. **Time Range:** Use Grafana's built-in time picker

---

## 📈 **Panel 1: Total Deal Value by Portfolio**

**Panel Type:** Table  
**Title:** "💰 Total Deal Value by Portfolio"

```sql
SELECT 
    p.id as portfolio_id,
    p.name as "Portfolio Name",
    ROUND(COALESCE(SUM(ccs.deal_value), 0) / 100.0, 2) as "Total Deal Value (R$)",
    COUNT(ccs.id) as "Deal Count"
FROM business_base.portfolio p
LEFT JOIN business_base.collect_cash_stats ccs ON p.id = ccs.portfolio_id 
    AND ccs.status = 'ACTIVE'
    AND ccs.created_at >= $__timeFrom()
    AND ccs.created_at <= $__timeTo()
WHERE p.customer_id = '${customer_id}'::uuid
    AND p.status = 'ACTIVE'
GROUP BY p.id, p.name
ORDER BY "Total Deal Value (R$)" DESC;
```

---

## 🎯 **Panel 2: Average Ticket Value**

**Panel Type:** Stat  
**Title:** "💵 Average Ticket Value"

```sql
SELECT 
    CASE 
        WHEN COUNT(pi.id) > 0 THEN ROUND(COALESCE(SUM(ccs.deal_value), 0) / COUNT(pi.id) / 100.0, 2)
        ELSE 0 
    END as "Average Ticket Value (R$)",
    ROUND(COALESCE(SUM(ccs.deal_value), 0) / 100.0, 2) as "Total Deal Value (R$)",
    COUNT(pi.id) as "Successful Negotiations Count"
FROM business_base.portfolio p
LEFT JOIN business_base.portfolio_item pi ON p.id = pi.portfolio_id 
    AND pi.current_status = 'SUCCEED' 
    AND pi.status = 'ACTIVE'
LEFT JOIN business_base.collect_cash_stats ccs ON pi.id = ccs.portfolio_item_id 
    AND ccs.status = 'ACTIVE'
    AND ccs.created_at >= $__timeFrom()
    AND ccs.created_at <= $__timeTo()
WHERE p.customer_id = '${customer_id}'::uuid
    AND p.status = 'ACTIVE';
```

---

## 📦 **Panel 3: Total Portfolio Quantity**

**Panel Type:** Stat  
**Title:** "📦 Total Portfolio Quantity"

```sql
SELECT 
    COALESCE(SUM(p.total_quantity), 0) as "Total Portfolio Quantity"
FROM business_base.portfolio p
WHERE p.customer_id = '${customer_id}'::uuid
    AND p.status = 'ACTIVE';
```

---

## 🎯 **Panel 4: Successful Negotiations with Interactions (Daily)**

**Panel Type:** Time Series (Bars)  
**Title:** "🎯 Daily Successful Negotiations with Interactions"

```sql
SELECT 
    DATE(pi.created_at) as time,
    COUNT(pi.id) as "Successful Negotiations with Interactions"
FROM business_base.portfolio p
JOIN business_base.portfolio_item pi ON p.id = pi.portfolio_id
WHERE p.customer_id = '${customer_id}'::uuid
    AND p.status = 'ACTIVE'
    AND pi.current_status = 'SUCCEED'
    AND pi.last_interaction IS NOT NULL
    AND pi.status = 'ACTIVE'
    AND pi.created_at >= $__timeFrom()
    AND pi.created_at <= $__timeTo()
GROUP BY DATE(pi.created_at)
ORDER BY time;
```

---

## 📥 **Panel 5: Daily Portfolio Imports**

**Panel Type:** Time Series (Bars)  
**Title:** "📥 Daily Portfolio Imports"

```sql
SELECT 
    DATE(p.created_at) as time,
    COUNT(p.id) as "Portfolios Imported"
FROM business_base.portfolio p
WHERE p.customer_id = '${customer_id}'::uuid
    AND p.status = 'ACTIVE'
    AND p.created_at >= $__timeFrom()
    AND p.created_at <= $__timeTo()
GROUP BY DATE(p.created_at)
ORDER BY time;
```

---

## 📧 **Panel 6: Daily First Messages Sent**

**Panel Type:** Time Series (Line)  
**Title:** "📧 Daily First Messages Sent"

```sql
SELECT 
    DATE(om.sent_at) as time,
    COUNT(om.id) as "First Messages Sent"
FROM message_hub.outgoing_message om
WHERE om.customer_id = '${customer_id}'::uuid
    AND om.sent = true
    AND om.is_first_message = true
    AND om.sent_at >= $__timeFrom()
    AND om.sent_at <= $__timeTo()
GROUP BY DATE(om.sent_at)
ORDER BY time;
```

---

## 📊 **Panel 7: Success Rate Analysis**

**Panel Type:** Stat  
**Title:** "📊 Success Rate Analysis"

```sql
SELECT 
    COUNT(CASE WHEN pi.current_status = 'SUCCEED' THEN 1 END) as "Successful Negotiations",
    COUNT(CASE WHEN pi.last_interaction IS NOT NULL THEN 1 END) as "Total Interactions",
    CASE 
        WHEN COUNT(CASE WHEN pi.last_interaction IS NOT NULL THEN 1 END) > 0 
        THEN ROUND(
            (COUNT(CASE WHEN pi.current_status = 'SUCCEED' THEN 1 END)::decimal / 
             COUNT(CASE WHEN pi.last_interaction IS NOT NULL THEN 1 END)::decimal) * 100, 2
        )
        ELSE 0 
    END as "Success Rate (%)"
FROM business_base.portfolio p
JOIN business_base.portfolio_item pi ON p.id = pi.portfolio_id
WHERE p.customer_id = '${customer_id}'::uuid
    AND p.status = 'ACTIVE'
    AND pi.status = 'ACTIVE'
    AND pi.created_at >= $__timeFrom()
    AND pi.created_at <= $__timeTo();
```

---

## 💰 **Panel 8: Daily Recovered Value**

**Panel Type:** Time Series (Line)  
**Title:** "💰 Daily Recovered Value"

```sql
SELECT 
    DATE(ccs.created_at) as time,
    ROUND(COALESCE(SUM(ccs.deal_value), 0) / 100.0, 2) as "Daily Recovered Value (R$)",
    COUNT(ccs.id) as "Deals Count"
FROM business_base.collect_cash_stats ccs
JOIN business_base.portfolio p ON ccs.portfolio_id = p.id
WHERE p.customer_id = '${customer_id}'::uuid
    AND ccs.status = 'ACTIVE'
    AND p.status = 'ACTIVE'
    AND ccs.created_at >= $__timeFrom()
    AND ccs.created_at <= $__timeTo()
GROUP BY DATE(ccs.created_at)
ORDER BY time;
```

---

## 📋 **Panel 9: Comprehensive Dashboard Summary**

**Panel Type:** Stat  
**Title:** "📋 Comprehensive Dashboard Summary"

```sql
WITH portfolio_stats AS (
    SELECT 
        COUNT(DISTINCT p.id) as total_portfolios,
        COALESCE(SUM(p.total_quantity), 0) as total_portfolio_quantity
    FROM business_base.portfolio p
    WHERE p.customer_id = '${customer_id}'::uuid AND p.status = 'ACTIVE'
),
deal_stats AS (
    SELECT 
        COALESCE(SUM(ccs.deal_value), 0) as total_recovered_value,
        COUNT(ccs.id) as total_deals
    FROM business_base.collect_cash_stats ccs
    JOIN business_base.portfolio p ON ccs.portfolio_id = p.id
    WHERE p.customer_id = '${customer_id}'::uuid 
        AND ccs.status = 'ACTIVE' 
        AND p.status = 'ACTIVE'
        AND ccs.created_at >= $__timeFrom()
        AND ccs.created_at <= $__timeTo()
),
interaction_stats AS (
    SELECT 
        COUNT(CASE WHEN pi.current_status = 'SUCCEED' THEN 1 END) as successful_negotiations,
        COUNT(CASE WHEN pi.last_interaction IS NOT NULL THEN 1 END) as total_interactions,
        COUNT(CASE WHEN pi.current_status = 'SUCCEED' AND pi.last_interaction IS NOT NULL THEN 1 END) as successful_with_interactions
    FROM business_base.portfolio p
    JOIN business_base.portfolio_item pi ON p.id = pi.portfolio_id
    WHERE p.customer_id = '${customer_id}'::uuid 
        AND p.status = 'ACTIVE' 
        AND pi.status = 'ACTIVE'
        AND pi.created_at >= $__timeFrom()
        AND pi.created_at <= $__timeTo()
)
SELECT 
    ps.total_portfolios as "Total Portfolios",
    ps.total_portfolio_quantity as "Total Portfolio Quantity",
    ROUND(ds.total_recovered_value / 100.0, 2) as "Total Recovered Value (R$)",
    ds.total_deals as "Total Deals",
    CASE 
        WHEN is_stats.successful_negotiations > 0 
        THEN ROUND((ds.total_recovered_value / is_stats.successful_negotiations) / 100.0, 2)
        ELSE 0 
    END as "Average Ticket Value (R$)",
    is_stats.successful_negotiations as "Successful Negotiations",
    is_stats.total_interactions as "Total Interactions",
    is_stats.successful_with_interactions as "Successful with Interactions",
    CASE 
        WHEN is_stats.total_interactions > 0 
        THEN ROUND((is_stats.successful_negotiations::decimal / is_stats.total_interactions::decimal) * 100, 2)
        ELSE 0 
    END as "Success Rate (%)"
FROM portfolio_stats ps, deal_stats ds, interaction_stats is_stats;
```

---

# 🌍 **OVERALL QUERIES (All Customers)**

## 📊 **Overall Panel 1: All Customers Performance Overview**

**Panel Type:** Table
**Title:** "🌍 All Customers Performance Overview"

```sql
WITH customer_data AS (
  SELECT
    c.id as customer_id,
    c.name as customer_name,
    COUNT(DISTINCT p.id) as total_portfolios,
    COALESCE(SUM(p.total_quantity), 0) as total_portfolio_quantity
  FROM business_base.customer c
  LEFT JOIN business_base.portfolio p ON c.id = p.customer_id AND p.status = 'ACTIVE'
  WHERE c.status = 'ACTIVE'
  GROUP BY c.id, c.name
),
deal_data AS (
  SELECT
    c.id as customer_id,
    COALESCE(SUM(ccs.deal_value), 0) as total_recovered_value,
    COUNT(ccs.id) as total_deals
  FROM business_base.customer c
  LEFT JOIN business_base.portfolio p ON c.id = p.customer_id AND p.status = 'ACTIVE'
  LEFT JOIN business_base.collect_cash_stats ccs ON p.id = ccs.portfolio_id
    AND ccs.status = 'ACTIVE'
    AND ccs.created_at >= $__timeFrom()
    AND ccs.created_at <= $__timeTo()
  WHERE c.status = 'ACTIVE'
  GROUP BY c.id
),
interaction_data AS (
  SELECT
    c.id as customer_id,
    COUNT(CASE WHEN pi.current_status = 'SUCCEED' THEN 1 END) as successful_negotiations,
    COUNT(CASE WHEN pi.last_interaction IS NOT NULL THEN 1 END) as total_interactions
  FROM business_base.customer c
  LEFT JOIN business_base.portfolio p ON c.id = p.customer_id AND p.status = 'ACTIVE'
  LEFT JOIN business_base.portfolio_item pi ON p.id = pi.portfolio_id
    AND pi.status = 'ACTIVE'
    AND pi.created_at >= $__timeFrom()
    AND pi.created_at <= $__timeTo()
  WHERE c.status = 'ACTIVE'
  GROUP BY c.id
)
SELECT
  cd.customer_name as "Customer",
  cd.total_portfolios as "Portfolios",
  cd.total_portfolio_quantity as "Total Items",
  ROUND(dd.total_recovered_value / 100.0, 2) as "Recovered Value (R$)",
  dd.total_deals as "Deals",
  CASE
    WHEN id_data.successful_negotiations > 0
    THEN ROUND((dd.total_recovered_value / id_data.successful_negotiations) / 100.0, 2)
    ELSE 0
  END as "Avg Ticket (R$)",
  id_data.successful_negotiations as "Successful",
  id_data.total_interactions as "Interactions",
  CASE
    WHEN id_data.total_interactions > 0
    THEN ROUND((id_data.successful_negotiations::decimal / id_data.total_interactions::decimal) * 100, 2)
    ELSE 0
  END as "Success Rate (%)"
FROM customer_data cd
LEFT JOIN deal_data dd ON cd.customer_id = dd.customer_id
LEFT JOIN interaction_data id_data ON cd.customer_id = id_data.customer_id
ORDER BY dd.total_recovered_value DESC;
```

---

## 📈 **Overall Panel 2: Total Metrics Summary (All Customers)**

**Panel Type:** Stat
**Title:** "📈 Total Metrics Summary (All Customers)"

```sql
SELECT
  COUNT(DISTINCT c.id) as "Total Customers",
  COUNT(DISTINCT p.id) as "Total Portfolios",
  COALESCE(SUM(p.total_quantity), 0) as "Total Items",
  ROUND(COALESCE(SUM(ccs.deal_value), 0) / 100.0, 2) as "Total Recovered (R$)",
  COUNT(ccs.id) as "Total Deals",
  COUNT(CASE WHEN pi.current_status = 'SUCCEED' THEN 1 END) as "Total Successful",
  COUNT(CASE WHEN pi.last_interaction IS NOT NULL THEN 1 END) as "Total Interactions",
  CASE
    WHEN COUNT(CASE WHEN pi.last_interaction IS NOT NULL THEN 1 END) > 0
    THEN ROUND((COUNT(CASE WHEN pi.current_status = 'SUCCEED' THEN 1 END)::decimal / COUNT(CASE WHEN pi.last_interaction IS NOT NULL THEN 1 END)::decimal) * 100, 2)
    ELSE 0
  END as "Overall Success Rate (%)"
FROM business_base.customer c
LEFT JOIN business_base.portfolio p ON c.id = p.customer_id AND p.status = 'ACTIVE'
LEFT JOIN business_base.collect_cash_stats ccs ON p.id = ccs.portfolio_id
  AND ccs.status = 'ACTIVE'
  AND ccs.created_at >= $__timeFrom()
  AND ccs.created_at <= $__timeTo()
LEFT JOIN business_base.portfolio_item pi ON p.id = pi.portfolio_id
  AND pi.status = 'ACTIVE'
  AND pi.created_at >= $__timeFrom()
  AND pi.created_at <= $__timeTo()
WHERE c.status = 'ACTIVE';
```

---

## 🏆 **Overall Panel 3: Top 10 Customers by Recovered Value**

**Panel Type:** Bar Chart (Horizontal)
**Title:** "🏆 Top 10 Customers by Recovered Value"

```sql
SELECT
  c.name as customer_name,
  ROUND(COALESCE(SUM(ccs.deal_value), 0) / 100.0, 2) as "Recovered Value (R$)"
FROM business_base.customer c
LEFT JOIN business_base.portfolio p ON c.id = p.customer_id AND p.status = 'ACTIVE'
LEFT JOIN business_base.collect_cash_stats ccs ON p.id = ccs.portfolio_id
  AND ccs.status = 'ACTIVE'
  AND ccs.created_at >= $__timeFrom()
  AND ccs.created_at <= $__timeTo()
WHERE c.status = 'ACTIVE'
GROUP BY c.id, c.name
HAVING COALESCE(SUM(ccs.deal_value), 0) > 0
ORDER BY "Recovered Value (R$)" DESC
LIMIT 10;
```

---

## 📊 **Overall Panel 4: Daily Recovered Value Trend (All Customers)**

**Panel Type:** Time Series
**Title:** "📊 Daily Recovered Value Trend (All Customers)"

```sql
SELECT
    DATE(ccs.created_at) as time,
    ROUND(COALESCE(SUM(ccs.deal_value), 0) / 100.0, 2) as "Daily Recovered Value (R$)",
    COUNT(ccs.id) as "Daily Deals Count"
FROM business_base.collect_cash_stats ccs
JOIN business_base.portfolio p ON ccs.portfolio_id = p.id
JOIN business_base.customer c ON p.customer_id = c.id
WHERE ccs.status = 'ACTIVE'
    AND p.status = 'ACTIVE'
    AND c.status = 'ACTIVE'
    AND ccs.created_at >= $__timeFrom()
    AND ccs.created_at <= $__timeTo()
GROUP BY DATE(ccs.created_at)
ORDER BY time;
```
