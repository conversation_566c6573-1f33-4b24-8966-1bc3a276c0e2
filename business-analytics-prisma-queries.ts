// Business Analytics Metrics - Prisma Query Implementations
// Based on collect-cash-stats, portfolio, and portfolio-item schemas

import { PrismaService } from '@common/db/prisma.service';
import { Prisma } from '@prisma/client';

export interface BusinessAnalyticsMetrics {
  totalDealValueByPortfolio: PortfolioDealValue[];
  averageTicketValue: number;
  totalPortfolioQuantity: number;
  successfulNegotiationsWithInteractions: DailyMetric[];
  dailyImports: DailyMetric[];
  dailyInitiatedContacts: DailyMetric[];
  successRate: SuccessRateMetric;
  dailyRecoveredValue: DailyMetric[];
  dashboardSummary: DashboardSummary;
}

export interface PortfolioDealValue {
  portfolioId: string;
  portfolioName: string;
  totalDealValue: number;
  dealCount: number;
}

export interface DailyMetric {
  date: string;
  value: number;
  count?: number;
}

export interface SuccessRateMetric {
  successfulNegotiations: number;
  totalInteractions: number;
  successRatePercentage: number;
}

export interface DashboardSummary {
  totalPortfolios: number;
  totalPortfolioQuantity: number;
  totalRecoveredValue: number;
  totalDeals: number;
  averageTicketValue: number;
  successfulNegotiations: number;
  totalInteractions: number;
  successfulWithInteractions: number;
  successRatePercentage: number;
}

export class BusinessAnalyticsService {
  constructor(private readonly prisma: PrismaService) {}

  // 1. Total Deal Value by Portfolio
  async getTotalDealValueByPortfolio(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<PortfolioDealValue[]> {
    const whereClause: any = {
      status: 'ACTIVE',
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) whereClause.createdAt.gte = startDate;
      if (endDate) whereClause.createdAt.lte = endDate;
    }

    const portfolios = await this.prisma.client.portfolio.findMany({
      where: {
        customerId,
        status: 'ACTIVE',
      },
      select: {
        id: true,
        name: true,
      },
    });

    const results: PortfolioDealValue[] = [];

    for (const portfolio of portfolios) {
      const dealStats = await this.prisma.client.collectCashStats.aggregate({
        where: {
          portfolioId: portfolio.id,
          ...whereClause,
        },
        _sum: {
          dealValue: true,
        },
        _count: {
          id: true,
        },
      });

      results.push({
        portfolioId: portfolio.id,
        portfolioName: portfolio.name,
        totalDealValue: Number(dealStats._sum.dealValue) || 0,
        dealCount: dealStats._count.id,
      });
    }

    return results.sort((a, b) => b.totalDealValue - a.totalDealValue);
  }

  // 2. Average Ticket Value
  async getAverageTicketValue(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    // Get total deal value
    const totalDealValue = await this.getTotalDealValueByCustomer(customerId, startDate, endDate);

    // Get successful negotiations count
    const successfulCount = await this.getSuccessfulNegotiationsCount(customerId, startDate, endDate);

    return successfulCount > 0 ? totalDealValue / successfulCount : 0;
  }

  // 3. Total Portfolio Quantity
  async getTotalPortfolioQuantity(customerId: string): Promise<number> {
    const result = await this.prisma.client.portfolio.aggregate({
      where: {
        customerId,
        status: 'ACTIVE',
      },
      _sum: {
        totalQuantity: true,
      },
    });

    return Number(result._sum.totalQuantity) || 0;
  }

  // 4. Successful Negotiations with Interactions (Daily)
  async getSuccessfulNegotiationsWithInteractions(
    customerId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<DailyMetric[]> {
    const query = Prisma.sql`
      SELECT 
        DATE(pi.created_at) as date,
        COUNT(pi.id)::int as value
      FROM business_base.portfolio p
      JOIN business_base.portfolio_item pi ON p.id = pi.portfolio_id
      WHERE p.customer_id = ${customerId}::uuid
        AND p.status = 'ACTIVE'
        AND pi.current_status = 'SUCCEED'
        AND pi.last_interaction IS NOT NULL
        AND pi.status = 'ACTIVE'
        AND pi.created_at >= ${startDate}::timestamp
        AND pi.created_at <= ${endDate}::timestamp
      GROUP BY DATE(pi.created_at)
      ORDER BY date
    `;

    const results = await this.prisma.client.$queryRaw<{ date: Date; value: number }[]>(query);
    
    return results.map(row => ({
      date: row.date.toISOString().split('T')[0],
      value: row.value,
    }));
  }

  // 5. Daily Imports (Portfolio Creation)
  async getDailyImports(
    customerId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<DailyMetric[]> {
    const query = Prisma.sql`
      SELECT 
        DATE(p.created_at) as date,
        COUNT(p.id)::int as value
      FROM business_base.portfolio p
      WHERE p.customer_id = ${customerId}::uuid
        AND p.status = 'ACTIVE'
        AND p.created_at >= ${startDate}::timestamp
        AND p.created_at <= ${endDate}::timestamp
      GROUP BY DATE(p.created_at)
      ORDER BY date
    `;

    const results = await this.prisma.client.$queryRaw<{ date: Date; value: number }[]>(query);
    
    return results.map(row => ({
      date: row.date.toISOString().split('T')[0],
      value: row.value,
    }));
  }

  // 6. Daily Initiated Contacts (First Messages Sent)
  async getDailyInitiatedContacts(
    customerId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<DailyMetric[]> {
    const query = Prisma.sql`
      SELECT 
        DATE(om.sent_at) as date,
        COUNT(om.id)::int as value
      FROM message_hub.outgoing_message om
      WHERE om.customer_id = ${customerId}::uuid
        AND om.sent = true
        AND om.is_first_message = true
        AND om.sent_at >= ${startDate}::timestamp
        AND om.sent_at <= ${endDate}::timestamp
      GROUP BY DATE(om.sent_at)
      ORDER BY date
    `;

    const results = await this.prisma.client.$queryRaw<{ date: Date; value: number }[]>(query);
    
    return results.map(row => ({
      date: row.date.toISOString().split('T')[0],
      value: row.value,
    }));
  }

  // 7. Success Rate Calculation
  async getSuccessRate(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<SuccessRateMetric> {
    const whereClause: any = {
      portfolio: {
        customerId,
        status: 'ACTIVE',
      },
      status: 'ACTIVE',
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) whereClause.createdAt.gte = startDate;
      if (endDate) whereClause.createdAt.lte = endDate;
    }

    const [successfulCount, totalInteractionsCount] = await Promise.all([
      this.prisma.client.portfolioItem.count({
        where: {
          ...whereClause,
          currentStatus: 'SUCCEED',
        },
      }),
      this.prisma.client.portfolioItem.count({
        where: {
          ...whereClause,
          lastInteraction: {
            not: null,
          },
        },
      }),
    ]);

    const successRatePercentage = totalInteractionsCount > 0 
      ? Math.round((successfulCount / totalInteractionsCount) * 100 * 100) / 100 
      : 0;

    return {
      successfulNegotiations: successfulCount,
      totalInteractions: totalInteractionsCount,
      successRatePercentage,
    };
  }

  // 8. Daily Recovered Value
  async getDailyRecoveredValue(
    customerId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<DailyMetric[]> {
    const query = Prisma.sql`
      SELECT 
        DATE(ccs.created_at) as date,
        COALESCE(SUM(ccs.deal_value), 0)::int as value,
        COUNT(ccs.id)::int as count
      FROM business_base.collect_cash_stats ccs
      JOIN business_base.portfolio p ON ccs.portfolio_id = p.id
      WHERE p.customer_id = ${customerId}::uuid
        AND ccs.status = 'ACTIVE'
        AND p.status = 'ACTIVE'
        AND ccs.created_at >= ${startDate}::timestamp
        AND ccs.created_at <= ${endDate}::timestamp
      GROUP BY DATE(ccs.created_at)
      ORDER BY date
    `;

    const results = await this.prisma.client.$queryRaw<{ date: Date; value: number; count: number }[]>(query);
    
    return results.map(row => ({
      date: row.date.toISOString().split('T')[0],
      value: row.value,
      count: row.count,
    }));
  }

  // 9. Comprehensive Dashboard Summary
  async getDashboardSummary(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<DashboardSummary> {
    const [
      totalPortfolioQuantity,
      totalDealValue,
      successRate,
      portfolioCount,
      totalDeals,
    ] = await Promise.all([
      this.getTotalPortfolioQuantity(customerId),
      this.getTotalDealValueByCustomer(customerId, startDate, endDate),
      this.getSuccessRate(customerId, startDate, endDate),
      this.getPortfolioCount(customerId),
      this.getTotalDealsCount(customerId, startDate, endDate),
    ]);

    const averageTicketValue = successRate.successfulNegotiations > 0 
      ? totalDealValue / successRate.successfulNegotiations 
      : 0;

    return {
      totalPortfolios: portfolioCount,
      totalPortfolioQuantity,
      totalRecoveredValue: totalDealValue,
      totalDeals,
      averageTicketValue,
      successfulNegotiations: successRate.successfulNegotiations,
      totalInteractions: successRate.totalInteractions,
      successfulWithInteractions: await this.getSuccessfulWithInteractionsCount(customerId, startDate, endDate),
      successRatePercentage: successRate.successRatePercentage,
    };
  }

  // Helper methods
  private async getTotalDealValueByCustomer(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    const whereClause: any = {
      portfolio: {
        customerId,
        status: 'ACTIVE',
      },
      status: 'ACTIVE',
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) whereClause.createdAt.gte = startDate;
      if (endDate) whereClause.createdAt.lte = endDate;
    }

    const result = await this.prisma.client.collectCashStats.aggregate({
      where: whereClause,
      _sum: {
        dealValue: true,
      },
    });

    return Number(result._sum.dealValue) || 0;
  }

  private async getSuccessfulNegotiationsCount(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    const whereClause: any = {
      portfolio: {
        customerId,
        status: 'ACTIVE',
      },
      currentStatus: 'SUCCEED',
      status: 'ACTIVE',
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) whereClause.createdAt.gte = startDate;
      if (endDate) whereClause.createdAt.lte = endDate;
    }

    return this.prisma.client.portfolioItem.count({
      where: whereClause,
    });
  }

  private async getPortfolioCount(customerId: string): Promise<number> {
    return this.prisma.client.portfolio.count({
      where: {
        customerId,
        status: 'ACTIVE',
      },
    });
  }

  private async getTotalDealsCount(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    const whereClause: any = {
      portfolio: {
        customerId,
        status: 'ACTIVE',
      },
      status: 'ACTIVE',
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) whereClause.createdAt.gte = startDate;
      if (endDate) whereClause.createdAt.lte = endDate;
    }

    return this.prisma.client.collectCashStats.count({
      where: whereClause,
    });
  }

  private async getSuccessfulWithInteractionsCount(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    const whereClause: any = {
      portfolio: {
        customerId,
        status: 'ACTIVE',
      },
      currentStatus: 'SUCCEED',
      lastInteraction: {
        not: null,
      },
      status: 'ACTIVE',
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) whereClause.createdAt.gte = startDate;
      if (endDate) whereClause.createdAt.lte = endDate;
    }

    return this.prisma.client.portfolioItem.count({
      where: whereClause,
    });
  }
}
